import { Module } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';
import { DatabaseModule } from './database/database.module';
import { AuthModule } from './auth/auth.module';
import { UsersModule } from './users/users.module';
import { ProductionsModule } from './productions/productions.module';
import { EventsModule } from './events/events.module';
import { WebSocketModule } from './websocket/websocket.module';
import { YouTubeModule } from './youtube/youtube.module';
import { AppController } from './app.controller';
import { AppService } from './app.service';

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    DatabaseModule,
    AuthModule,
    UsersModule,
    ProductionsModule,
    EventsModule,
    WebSocketModule,
    YouTubeModule,
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule {}
