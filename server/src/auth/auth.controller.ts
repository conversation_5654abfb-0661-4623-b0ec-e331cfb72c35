import { Controller, Request, Post, UseGuards, Body, Get, Res } from '@nestjs/common';
import { Response } from 'express';
import { AuthService } from './auth.service';
import { LocalAuthGuard } from './guards/local-auth.guard';
import { JwtAuthGuard } from './guards/jwt-auth.guard';
import { GoogleAuthGuard } from './guards/google-auth.guard';
import { RegisterDto } from './dto/register.dto';
import { LoginDto } from './dto/login.dto';

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @Post('register')
  async register(@Body() registerDto: RegisterDto) {
    return this.authService.register(registerDto);
  }

  @UseGuards(LocalAuthGuard)
  @Post('login')
  async login(@Request() req, @Body() loginDto: LoginDto) {
    return this.authService.login(req.user);
  }

  @Get('google')
  @UseGuards(GoogleAuthGuard)
  async googleAuth(@Request() req) {
    // Guard redirects to Google
  }

  @Get('google/callback')
  @UseGuards(GoogleAuthGuard)
  async googleAuthRedirect(@Request() req, @Res() res: Response) {
    const result = await this.authService.login(req.user);

    // Redirect to frontend with token
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
    res.redirect(`${frontendUrl}/auth/callback?token=${result.access_token}`);
  }

  // @Get('facebook')
  // @UseGuards(FacebookAuthGuard)
  // async facebookAuth(@Request() req) {
  //   // Guard redirects to Facebook
  // }

  // @Get('facebook/callback')
  // @UseGuards(FacebookAuthGuard)
  // async facebookAuthRedirect(@Request() req, @Res() res: Response) {
  //   const result = await this.authService.login(req.user);
  //
  //   // Redirect to frontend with token
  //   const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:5173';
  //   res.redirect(`${frontendUrl}/auth/callback?token=${result.access_token}`);
  // }

  @UseGuards(JwtAuthGuard)
  @Get('profile')
  async getProfile(@Request() req) {
    return this.authService.getProfile(req.user.userId);
  }

  @UseGuards(JwtAuthGuard)
  @Post('logout')
  async logout(@Request() req) {
    // For JWT, logout is handled client-side by removing the token
    return { message: 'Logged out successfully' };
  }
}
