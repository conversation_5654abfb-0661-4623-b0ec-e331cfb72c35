import { Injectable, UnauthorizedException } from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { UsersService } from '../users/users.service';
import { UserDocument } from '../users/schemas/user.schema';
import { CreateUserDto } from '../users/dto/create-user.dto';
import { RegisterDto } from './dto/register.dto';

@Injectable()
export class AuthService {
  constructor(
    private usersService: UsersService,
    private jwtService: JwtService,
  ) {}

  async validateUser(email: string, password: string): Promise<any> {
    const user = await this.usersService.findByEmail(email);
    if (user && await this.usersService.validatePassword(user, password)) {
      const { password, ...result } = user.toObject();
      return result;
    }
    return null;
  }

  async validateOAuthUser(profile: {
    email: string;
    name: string;
    avatar?: string;
    provider: string;
    providerId: string;
    accessToken?: string;
    refreshToken?: string;
  }): Promise<UserDocument> {
    // First try to find user by provider ID
    let user = await this.usersService.findByProvider(profile.provider, profile.providerId);
    
    if (!user) {
      // Try to find by email (user might have registered with email/password first)
      user = await this.usersService.findByEmail(profile.email);
      
      if (user) {
        // Update existing user with OAuth info and tokens
        user.provider = profile.provider;
        user.providerId = profile.providerId;
        if (profile.avatar && !user.avatar) {
          user.avatar = profile.avatar;
        }
        if (profile.accessToken) {
          user.accessToken = profile.accessToken;
          user.tokenExpiresAt = new Date(Date.now() + 3600 * 1000); // 1 hour from now
        }
        if (profile.refreshToken) {
          user.refreshToken = profile.refreshToken;
        }
        await user.save();
      } else {
        // Create new user
        const createUserDto: CreateUserDto = {
          email: profile.email,
          name: profile.name,
          avatar: profile.avatar,
          provider: profile.provider,
          providerId: profile.providerId,
          accessToken: profile.accessToken,
          refreshToken: profile.refreshToken,
        };
        user = await this.usersService.create(createUserDto);
      }
    }

    return user;
  }

  async login(user: any) {
    const payload = { 
      email: user.email, 
      sub: user._id,
      name: user.name 
    };
    
    return {
      access_token: this.jwtService.sign(payload),
      user: {
        id: user._id,
        email: user.email,
        name: user.name,
        avatar: user.avatar,
        currentRoomId: user.currentRoomId,
      },
    };
  }

  async register(registerDto: RegisterDto) {
    const createUserDto: CreateUserDto = {
      email: registerDto.email,
      password: registerDto.password,
      name: registerDto.name,
      provider: 'local',
    };

    const user = await this.usersService.create(createUserDto);
    return this.login(user);
  }

  async getProfile(userId: string) {
    const user = await this.usersService.findById(userId);
    return {
      id: user._id,
      email: user.email,
      name: user.name,
      avatar: user.avatar,
      currentProductionId: user.currentProductionId,
      provider: user.provider,
      lastLoginAt: user.lastLoginAt,
    };
  }
}
