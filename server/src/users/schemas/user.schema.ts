import { <PERSON>p, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type UserDocument = User & Document;

@Schema({ timestamps: true })
export class User {
  @Prop({ required: true, unique: true })
  email: string;

  @Prop()
  password?: string; // Optional for OAuth users

  @Prop({ required: true })
  name: string;

  @Prop()
  avatar?: string;

  @Prop({ type: String, enum: ['local', 'google', 'facebook'], default: 'local' })
  provider: string;

  @Prop()
  providerId?: string; // ID from OAuth provider

  @Prop()
  accessToken?: string; // OAuth access token for API calls

  @Prop()
  refreshToken?: string; // OAuth refresh token

  @Prop()
  tokenExpiresAt?: Date; // When the access token expires

  @Prop({ type: Types.ObjectId, ref: 'Production' })
  currentProductionId?: Types.ObjectId;

  @Prop({ default: Date.now })
  lastLoginAt: Date;

  @Prop({ default: true })
  isActive: boolean;
}

export const UserSchema = SchemaFactory.createForClass(User);

// Create indexes for efficient lookups
// Note: email index is created automatically by unique: true
UserSchema.index({ providerId: 1, provider: 1 });
UserSchema.index({ currentProductionId: 1 });
