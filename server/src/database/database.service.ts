import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { MongoMemoryServer } from 'mongodb-memory-server';

@Injectable()
export class DatabaseService implements OnModuleInit, OnModuleDestroy {
  private mongod: MongoMemoryServer;

  async onModuleInit() {
    // Create MongoDB Memory Server (in-memory only)
    this.mongod = await MongoMemoryServer.create({
      binary: {
        version: '7.0.0',
      },
    });

    console.log('📦 MongoDB Memory Server started (in-memory)');
    console.log(`🔗 Connection URI: ${this.mongod.getUri()}`);
  }

  async onModuleDestroy() {
    if (this.mongod) {
      await this.mongod.stop();
      console.log('📦 MongoDB Memory Server stopped');
    }
  }

  async getConnectionString(): Promise<string> {
    if (!this.mongod) {
      await this.onModuleInit();
    }
    return this.mongod.getUri();
  }

  getDbPath(): string {
    return 'in-memory';
  }
}
