import { IsString, IsOptional, IsDateString, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from 'class-validator';

export class CreateEventDto {
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name: string;

  @IsString()
  @IsOptional()
  @MaxLength(500)
  description?: string;

  @IsDateString()
  scheduledStartTime: string;

  @IsOptional()
  @IsEnum(['public', 'unlisted', 'private'])
  youtubePrivacy?: 'public' | 'unlisted' | 'private' = 'unlisted';
}
