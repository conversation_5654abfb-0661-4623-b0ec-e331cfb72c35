import { <PERSON>, Get, Post, Body, Patch, Param, Delete, UseGuards, Request, Query } from '@nestjs/common';
import { EventsService } from './events.service';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('events')
@UseGuards(JwtAuthGuard)
export class EventsController {
  constructor(private readonly eventsService: EventsService) {}

  @Post()
  create(@Body() createEventDto: CreateEventDto, @Request() req) {
    return this.eventsService.create(createEventDto, req.user.userId);
  }

  @Post('with-youtube')
  createWithYouTube(@Body() createEventDto: CreateEventDto, @Request() req) {
    return this.eventsService.createWithYouTube(createEventDto, req.user.userId);
  }

  @Get()
  findAll(@Query('my') my?: string, @Request() req?) {
    if (my === 'true') {
      return this.eventsService.findByHostUserId(req.user.userId);
    }
    return this.eventsService.findAll();
  }

  @Get('my')
  findMy(@Request() req, @Query('status') status?: string) {
    return this.eventsService.findByHostUserId(req.user.userId, status);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.eventsService.findById(id);
  }

  @Patch(':id')
  update(@Param('id') id: string, @Body() updateEventDto: UpdateEventDto, @Request() req) {
    return this.eventsService.update(id, updateEventDto, req.user.userId);
  }

  @Post(':id/go-live')
  goLive(@Param('id') id: string, @Request() req) {
    return this.eventsService.goLive(id, req.user.userId);
  }

  @Post(':id/complete')
  complete(@Param('id') id: string, @Request() req) {
    return this.eventsService.complete(id, req.user.userId);
  }

  @Delete(':id')
  remove(@Param('id') id: string, @Request() req) {
    return this.eventsService.remove(id, req.user.userId);
  }
}
