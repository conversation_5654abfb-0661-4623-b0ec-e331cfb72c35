import { Injectable, NotFoundException, ForbiddenException, ConflictException, Inject, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Event, EventDocument } from './schemas/event.schema';
import { CreateEventDto } from './dto/create-event.dto';
import { UpdateEventDto } from './dto/update-event.dto';
import { ProductionsService } from '../productions/productions.service';
import { YouTubeService } from '../youtube/youtube.service';

@Injectable()
export class EventsService {
  constructor(
    @InjectModel(Event.name) private eventModel: Model<EventDocument>,
    @Inject(forwardRef(() => ProductionsService)) private productionsService: ProductionsService,
    private youtubeService: YouTubeService,
  ) {}

  private generateShareableProductionId(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  async create(createEventDto: CreateEventDto, hostUserId: string): Promise<EventDocument> {
    const scheduledTime = new Date(createEventDto.scheduledStartTime);
    const now = new Date();

    // Allow events scheduled for now (within 1 minute) for "Go Live Now" functionality
    // Otherwise, validate that the scheduled time is in the future
    const timeDifferenceMinutes = (scheduledTime.getTime() - now.getTime()) / (1000 * 60);
    if (timeDifferenceMinutes < -1) {
      throw new ConflictException('Scheduled start time cannot be in the past');
    }

    // Generate unique shareable production ID
    let shareableProductionId = this.generateShareableProductionId();
    let attempts = 0;
    while (await this.eventModel.findOne({ shareableProductionId }).exec() && attempts < 10) {
      shareableProductionId = this.generateShareableProductionId();
      attempts++;
    }

    if (attempts >= 10) {
      throw new ConflictException('Failed to generate unique production ID');
    }

    const event = new this.eventModel({
      ...createEventDto,
      scheduledStartTime: scheduledTime,
      hostUserId: new Types.ObjectId(hostUserId),
      shareableProductionId,
    });

    return event.save();
  }

  async findAll(): Promise<EventDocument[]> {
    return this.eventModel.find({ status: { $ne: 'cancelled' } })
      .populate('hostUserId', 'name email')
      .sort({ scheduledStartTime: 1 })
      .exec();
  }

  async findByHostUserId(hostUserId: string, status?: string): Promise<EventDocument[]> {
    const query: any = {
      hostUserId: new Types.ObjectId(hostUserId),
      status: { $ne: 'cancelled' }
    };

    // If status is specified, filter by that status
    if (status) {
      query.status = status;
    }

    return this.eventModel.find(query)
      .populate('hostUserId', 'name email')
      .sort({ scheduledStartTime: 1 })
      .exec();
  }

  async findById(id: string): Promise<EventDocument> {
    const event = await this.eventModel.findById(id)
      .populate('hostUserId', 'name email')
      .exec();

    if (!event) {
      throw new NotFoundException('Event not found');
    }
    return event;
  }

  async findByProductionId(productionId: string): Promise<EventDocument | null> {
    return this.eventModel.findOne({ productionId: new Types.ObjectId(productionId) })
      .populate('hostUserId', 'name email')
      .exec();
  }

  async update(id: string, updateEventDto: UpdateEventDto, requestingUserId: string): Promise<EventDocument> {
    const event = await this.findById(id);

    // Only host can update event
    if (!event.hostUserId.equals(new Types.ObjectId(requestingUserId))) {
      throw new ForbiddenException('Only the host can update the event');
    }

    // Cannot update events that are live or completed
    if (event.status === 'live' || event.status === 'completed') {
      throw new ConflictException('Cannot update events that are live or completed');
    }

    // If updating scheduled time, validate it's in the future
    if (updateEventDto.scheduledStartTime) {
      const newScheduledTime = new Date(updateEventDto.scheduledStartTime);
      if (newScheduledTime <= new Date()) {
        throw new ConflictException('Scheduled start time must be in the future');
      }
    }

    Object.assign(event, updateEventDto);
    return event.save();
  }

  async goLive(id: string, requestingUserId: string): Promise<{ event: EventDocument; production: any }> {
    const event = await this.findById(id);

    // Only host can make event go live
    if (!event.hostUserId.equals(new Types.ObjectId(requestingUserId))) {
      throw new ForbiddenException('Only the host can make the event go live');
    }

    // Event must be scheduled
    if (event.status !== 'scheduled') {
      throw new ConflictException('Only scheduled events can go live');
    }

    // Check if user has an active production and complete it
    const existingProduction = await this.productionsService.findActiveByHostUserId(requestingUserId);
    if (existingProduction) {
      await this.productionsService.completeProduction(existingProduction.productionId, requestingUserId);
    }

    // Create new production for this event using the existing shareable production ID
    const production = await this.productionsService.create({
      name: event.name,
      productionId: event.shareableProductionId, // Use the pre-generated ID
    }, requestingUserId);

    // Update event status
    event.status = 'live';
    event.wentLiveAt = new Date();
    event.productionId = production._id as any;
    await event.save();

    return {
      event,
      production: {
        ...production.toObject(),
        productionId: production.productionId, // This is the shareable 6-character ID
        eventName: event.name // Include the event name
      }
    };
  }

  async complete(id: string, requestingUserId: string): Promise<EventDocument> {
    const event = await this.findById(id);

    // Only host can complete event
    if (!event.hostUserId.equals(new Types.ObjectId(requestingUserId))) {
      throw new ForbiddenException('Only the host can complete the event');
    }

    // Event must be live
    if (event.status !== 'live') {
      throw new ConflictException('Only live events can be completed');
    }

    // Complete the associated production if it exists
    if (event.productionId) {
      try {
        const production = await this.productionsService.findById(event.productionId.toString());
        if (production.status === 'active') {
          await this.productionsService.completeProduction(production.productionId, requestingUserId);
        }
      } catch (error) {
        // Production might already be completed, continue
      }
    }

    event.status = 'completed';
    event.completedAt = new Date();
    return event.save();
  }

  async remove(id: string, requestingUserId: string): Promise<void> {
    const event = await this.findById(id);

    // Only host can delete event
    if (!event.hostUserId.equals(new Types.ObjectId(requestingUserId))) {
      throw new ForbiddenException('Only the host can delete the event');
    }

    // Cannot delete live events
    if (event.status === 'live') {
      throw new ConflictException('Cannot delete live events. Complete the event first.');
    }

    event.status = 'cancelled';
    await event.save();
  }

  async createWithYouTube(
    createEventDto: CreateEventDto,
    hostUserId: string
  ): Promise<{ event: EventDocument; youtubeBroadcast?: any; youtubeStream?: any }> {
    // Create the event first
    const event = await this.create(createEventDto, hostUserId);

    try {
      // Create YouTube broadcast
      const youtubeBroadcast = await this.youtubeService.createBroadcast(
        hostUserId,
        event.name,
        event.description || '',
        event.scheduledStartTime,
        createEventDto.youtubePrivacy
      );

      // Create YouTube stream
      const youtubeStream = await this.youtubeService.createStream(
        hostUserId,
        `${event.name} - Stream`,
        event.description || '',
        '720p'
      );

      // Bind broadcast to stream
      await this.youtubeService.bindBroadcastToStream(
        hostUserId,
        youtubeBroadcast.id,
        youtubeStream.id
      );

      // Update event with YouTube details
      event.youtubeBroadcastId = youtubeBroadcast.id;
      event.youtubeStreamId = youtubeStream.id;
      await event.save();

      return { event, youtubeBroadcast, youtubeStream };
    } catch (error) {
      console.error('Failed to create YouTube broadcast/stream:', error);
      // Return event without YouTube integration
      return { event };
    }
  }
}
