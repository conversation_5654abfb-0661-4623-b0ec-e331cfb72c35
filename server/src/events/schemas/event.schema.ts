import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type EventDocument = Event & Document;

@Schema({ timestamps: true })
export class Event {
  @Prop({ required: true })
  name: string;

  @Prop()
  description?: string;

  @Prop({ required: true })
  scheduledStartTime: Date;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  hostUserId: Types.ObjectId;

  @Prop({ default: 'scheduled' })
  status: 'scheduled' | 'live' | 'completed' | 'cancelled';

  @Prop({ type: Types.ObjectId, ref: 'Production' })
  productionId?: Types.ObjectId; // Set when event goes live

  @Prop({ unique: true })
  shareableProductionId?: string; // 6-character shareable ID, generated on creation

  @Prop()
  wentLiveAt?: Date;

  @Prop()
  completedAt?: Date;

  // YouTube integration
  @Prop()
  youtubeBroadcastId?: string;

  @Prop()
  youtubeStreamId?: string;

  @Prop({ default: 'unlisted' })
  youtubePrivacy?: 'public' | 'unlisted' | 'private';
}

export const EventSchema = SchemaFactory.createForClass(Event);

// Create indexes for efficient lookups
EventSchema.index({ hostUserId: 1 });
EventSchema.index({ status: 1 });
EventSchema.index({ scheduledStartTime: 1 });
EventSchema.index({ hostUserId: 1, status: 1 });
EventSchema.index({ hostUserId: 1, scheduledStartTime: 1 });
// Note: shareableProductionId index is created automatically by unique: true
