import { Controller, Get, Post, Body, Patch, Param, Delete, UseGuards, Request } from '@nestjs/common';
import { ProductionsService } from './productions.service';
import { CreateProductionDto } from './dto/create-production.dto';
import { UpdateProductionDto } from './dto/update-production.dto';
import { JwtAuthGuard } from '../auth/guards/jwt-auth.guard';

@Controller('productions')
export class ProductionsController {
  constructor(private readonly productionsService: ProductionsService) {}

  @Post()
  @UseGuards(JwtAuthGuard)
  create(@Body() createProductionDto: CreateProductionDto, @Request() req) {
    return this.productionsService.create(createProductionDto, req.user.userId);
  }

  @Get()
  @UseGuards(JwtAuthGuard)
  findAll() {
    return this.productionsService.findAll();
  }

  @Get('my-active')
  @UseGuards(JwtAuthGuard)
  findMyActive(@Request() req) {
    return this.productionsService.findActiveByHostUserId(req.user.userId);
  }

  @Get('my-history')
  @UseGuards(JwtAuthGuard)
  findMyHistory(@Request() req) {
    return this.productionsService.findByHostUserId(req.user.userId);
  }

  @Get(':productionId')
  findOne(@Param('productionId') productionId: string) {
    return this.productionsService.findByProductionId(productionId);
  }

  @Patch(':productionId')
  @UseGuards(JwtAuthGuard)
  update(@Param('productionId') productionId: string, @Body() updateProductionDto: UpdateProductionDto, @Request() req) {
    return this.productionsService.update(productionId, updateProductionDto, req.user.userId);
  }

  @Post(':productionId/join')
  @UseGuards(JwtAuthGuard)
  joinProduction(@Param('productionId') productionId: string, @Request() req) {
    return this.productionsService.addParticipant(productionId, req.user.userId, req.user.userId);
  }

  @Post(':productionId/leave')
  @UseGuards(JwtAuthGuard)
  leaveProduction(@Param('productionId') productionId: string, @Request() req) {
    return this.productionsService.removeParticipant(productionId, req.user.userId);
  }

  @Post(':productionId/complete')
  @UseGuards(JwtAuthGuard)
  completeProduction(@Param('productionId') productionId: string, @Request() req) {
    return this.productionsService.completeProduction(productionId, req.user.userId);
  }

  @Post(':productionId/start-stream')
  @UseGuards(JwtAuthGuard)
  startStream(
    @Param('productionId') productionId: string,
    @Body() body: { rtmpUrl: string; streamKey: string },
    @Request() req
  ) {
    return this.productionsService.startStreaming(productionId, body.rtmpUrl, body.streamKey, req.user.userId);
  }

  @Post(':productionId/stop-stream')
  @UseGuards(JwtAuthGuard)
  stopStream(@Param('productionId') productionId: string, @Request() req) {
    return this.productionsService.stopStreaming(productionId, req.user.userId);
  }

  @Delete(':productionId')
  @UseGuards(JwtAuthGuard)
  remove(@Param('productionId') productionId: string, @Request() req) {
    return this.productionsService.remove(productionId, req.user.userId);
  }
}
