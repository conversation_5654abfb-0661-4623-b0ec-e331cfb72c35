import { Prop, Schema, SchemaFactory } from '@nestjs/mongoose';
import { Document, Types } from 'mongoose';

export type ProductionDocument = Production & Document;

@Schema({ timestamps: true })
export class Production {
  @Prop({ required: true, unique: true })
  productionId: string; // The 6-character production ID

  @Prop({ required: true })
  name: string;

  @Prop({ type: Types.ObjectId, ref: 'User', required: true })
  hostUserId: Types.ObjectId;

  @Prop({ type: [{ type: Types.ObjectId, ref: 'User' }], default: [] })
  participantUserIds: Types.ObjectId[];

  @Prop({ default: false })
  isStreaming: boolean;

  @Prop()
  rtmpUrl?: string;

  @Prop()
  rtmpStreamKey?: string;

  @Prop()
  streamStartedAt?: Date;

  @Prop()
  streamEndedAt?: Date;

  @Prop({ default: 'active' })
  status: 'active' | 'completed' | 'cancelled';

  @Prop({ default: Date.now })
  lastActivityAt: Date;

  // YouTube integration
  @Prop()
  youtubeBroadcastId?: string;

  @Prop()
  youtubeStreamId?: string;
}

export const ProductionSchema = SchemaFactory.createForClass(Production);

// Create indexes for efficient lookups
// Note: productionId index is created automatically by unique: true
ProductionSchema.index({ hostUserId: 1 });
ProductionSchema.index({ participantUserIds: 1 });
ProductionSchema.index({ status: 1, lastActivityAt: 1 });
ProductionSchema.index({ hostUserId: 1, status: 1 }); // For finding user's active production
