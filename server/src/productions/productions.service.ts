import { Injectable, ConflictException, NotFoundException, ForbiddenException, Inject, forwardRef } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { Production, ProductionDocument } from './schemas/production.schema';
import { CreateProductionDto } from './dto/create-production.dto';
import { UpdateProductionDto } from './dto/update-production.dto';
import { EventsService } from '../events/events.service';

@Injectable()
export class ProductionsService {
  constructor(
    @InjectModel(Production.name) private productionModel: Model<ProductionDocument>,
    @Inject(forwardRef(() => EventsService)) private eventsService?: EventsService,
  ) {}

  private generateProductionId(): string {
    return Math.random().toString(36).substring(2, 8).toUpperCase();
  }

  async create(createProductionDto: CreateProductionDto, hostUserId: string): Promise<ProductionDocument> {
    // Check if user already has an active production
    const existingProduction = await this.findActiveByHostUserId(hostUserId);
    if (existingProduction) {
      throw new ConflictException('User already has an active production. Complete the current production before creating a new one.');
    }

    let productionId = createProductionDto.productionId;

    // Generate production ID if not provided
    if (!productionId) {
      productionId = this.generateProductionId();

      // Ensure uniqueness
      let attempts = 0;
      while (await this.productionModel.findOne({ productionId }).exec() && attempts < 10) {
        productionId = this.generateProductionId();
        attempts++;
      }

      if (attempts >= 10) {
        throw new ConflictException('Unable to generate unique production ID');
      }
    } else {
      // Check if custom production ID already exists
      const existingProduction = await this.productionModel.findOne({ productionId, status: 'active' }).exec();
      if (existingProduction) {
        throw new ConflictException('Production with this ID already exists');
      }
    }

    const production = new this.productionModel({
      ...createProductionDto,
      productionId,
      hostUserId: new Types.ObjectId(hostUserId),
      status: 'active',
    });

    return production.save();
  }

  async findAll(): Promise<ProductionDocument[]> {
    return this.productionModel.find({ status: 'active' })
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .exec();
  }

  // Internal method that returns the actual Mongoose document
  private async findProductionDocument(productionId: string): Promise<ProductionDocument | null> {
    return this.productionModel.findOne({ productionId, status: 'active' })
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .exec();
  }

  // Public method that returns production with event name for API responses
  async findByProductionId(productionId: string): Promise<any> {
    const production = await this.findProductionDocument(productionId);

    if (!production) {
      return null;
    }

    // Try to find the associated event to get the event name
    let eventName = null;
    if (this.eventsService) {
      try {
        const event = await this.eventsService.findByProductionId(production._id.toString());
        if (event) {
          eventName = event.name;
        }
      } catch (error) {
        // Event might not exist, continue without event name
      }
    }

    return {
      ...production.toObject(),
      eventName
    };
  }

  async findById(id: string): Promise<ProductionDocument> {
    const production = await this.productionModel.findById(id)
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .exec();

    if (!production) {
      throw new NotFoundException('Production not found');
    }
    return production;
  }

  async findActiveByHostUserId(hostUserId: string): Promise<ProductionDocument | null> {
    return this.productionModel.findOne({
      hostUserId: new Types.ObjectId(hostUserId),
      status: 'active'
    })
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .exec();
  }

  async findByHostUserId(hostUserId: string): Promise<ProductionDocument[]> {
    return this.productionModel.find({ hostUserId: new Types.ObjectId(hostUserId) })
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .sort({ createdAt: -1 })
      .exec();
  }

  async findByParticipantUserId(participantUserId: string): Promise<ProductionDocument[]> {
    return this.productionModel.find({
      participantUserIds: new Types.ObjectId(participantUserId),
      status: 'active'
    })
      .populate('hostUserId', 'name email')
      .populate('participantUserIds', 'name email')
      .exec();
  }

  async addParticipant(productionId: string, participantUserId: string, requestingUserId: string): Promise<ProductionDocument> {
    const production = await this.findProductionDocument(productionId);
    if (!production) {
      throw new NotFoundException('Production not found');
    }

    const participantObjectId = new Types.ObjectId(participantUserId);

    // Check if user is already a participant
    if (production.participantUserIds.some(id => id.equals(participantObjectId))) {
      return production; // Already a participant
    }

    // Add participant
    production.participantUserIds.push(participantObjectId);
    production.lastActivityAt = new Date();

    return production.save();
  }

  async removeParticipant(productionId: string, participantUserId: string): Promise<ProductionDocument> {
    const production = await this.findProductionDocument(productionId);
    if (!production) {
      throw new NotFoundException('Production not found');
    }

    const participantObjectId = new Types.ObjectId(participantUserId);
    production.participantUserIds = production.participantUserIds.filter(id => !id.equals(participantObjectId));
    production.lastActivityAt = new Date();

    return production.save();
  }

  async completeProduction(productionId: string, requestingUserId: string): Promise<ProductionDocument> {
    const production = await this.findProductionDocument(productionId);
    if (!production) {
      throw new NotFoundException('Production not found');
    }

    // Only host can complete production
    if (!production.hostUserId.equals(new Types.ObjectId(requestingUserId))) {
      throw new ForbiddenException('Only the host can complete the production');
    }

    production.status = 'completed';
    production.streamEndedAt = new Date();
    production.isStreaming = false;

    const savedProduction = await production.save();

    // Complete the associated event if it exists and the events service is available
    if (this.eventsService) {
      try {
        // Find the event associated with this production
        const event = await this.eventsService.findByProductionId(production._id.toString());
        if (event && event.status === 'live') {
          await this.eventsService.complete(event._id.toString(), requestingUserId);
        }
      } catch (error) {
        // Event might not exist or already be completed, continue
        console.log('Could not complete associated event:', error.message);
      }
    }

    return savedProduction;
  }

  async update(productionId: string, updateProductionDto: UpdateProductionDto, requestingUserId: string): Promise<ProductionDocument> {
    const production = await this.findProductionDocument(productionId);
    if (!production) {
      throw new NotFoundException('Production not found');
    }

    // Only host can update production settings
    if (!production.hostUserId.equals(new Types.ObjectId(requestingUserId))) {
      throw new ForbiddenException('Only the host can update production settings');
    }

    Object.assign(production, updateProductionDto);
    production.lastActivityAt = new Date();

    return production.save();
  }

  async startStreaming(productionId: string, rtmpUrl: string, streamKey: string, requestingUserId: string): Promise<ProductionDocument> {
    const production = await this.update(productionId, {
      isStreaming: true,
      rtmpUrl,
      rtmpStreamKey: streamKey,
    }, requestingUserId);

    // Update stream started time if not already set
    if (!production.streamStartedAt) {
      production.streamStartedAt = new Date();
      await production.save();
    }

    return production;
  }

  async stopStreaming(productionId: string, requestingUserId: string): Promise<ProductionDocument> {
    return this.update(productionId, {
      isStreaming: false,
      rtmpUrl: undefined,
      rtmpStreamKey: undefined,
    }, requestingUserId);
  }

  async remove(productionId: string, requestingUserId: string): Promise<void> {
    const production = await this.findProductionDocument(productionId);
    if (!production) {
      throw new NotFoundException('Production not found');
    }

    // Only host can delete production
    if (!production.hostUserId.equals(new Types.ObjectId(requestingUserId))) {
      throw new ForbiddenException('Only the host can delete the production');
    }

    production.status = 'cancelled';
    await production.save();
  }
}
