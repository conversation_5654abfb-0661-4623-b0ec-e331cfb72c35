import React, { useState, useCallback } from 'react';
import { VideoCompositor } from './VideoCompositor';
import { RTMPStreaming } from './RTMPStreaming';
import { LayoutMode } from './webgl/layoutUtils';

interface StreamingHostProps {
  peers: Record<string, any>;
  peerIds: string[];
  localStream: MediaStream | null;
  overlayVideoUrl?: string;
}

export const StreamingHost: React.FC<StreamingHostProps> = ({
  peers,
  peerIds,
  localStream,
  overlayVideoUrl,
}) => {
  const [layoutMode, setLayoutMode] = useState<LayoutMode>('grid');
  const [canvasStream, setCanvasStream] = useState<MediaStream | null>(null);

  const handleCanvasStreamChange = useCallback((stream: MediaStream | null) => {
    setCanvasStream(stream);
    console.log('Canvas stream updated:', stream ? 'Available' : 'Stopped');
  }, []);

  const handleLayoutModeChange = useCallback((mode: LayoutMode) => {
    setLayoutMode(mode);
  }, []);

  return (
    <div className="min-h-screen bg-gray-100 p-4">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Streaming Host Dashboard
          </h1>
          <p className="text-gray-600">
            WebRTC to RTMP Media Streaming
          </p>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          {/* Main Video Compositor */}
          <div className="xl:col-span-3">
            <div className="bg-white rounded-lg shadow-md p-4">
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold">Video Compositor</h2>
                <div className="flex items-center space-x-2">
                  <div
                    className={`w-3 h-3 rounded-full ${
                      canvasStream ? 'bg-green-500' : 'bg-gray-400'
                    }`}
                  />
                  <span className="text-sm text-gray-600">
                    Canvas Stream: {canvasStream ? 'Active' : 'Inactive'}
                  </span>
                </div>
              </div>
              
              <VideoCompositor
                peers={peers}
                peerIds={peerIds}
                localStream={localStream}
                overlayVideoUrl={overlayVideoUrl}
                showOverlays={true}
                layoutMode={layoutMode}
                onLayoutModeChange={handleLayoutModeChange}
                onCanvasStreamChange={handleCanvasStreamChange}
                videoOverlayOpacity={1}
                iconOverlayOpacity={1}
              />
            </div>
          </div>

          {/* Control Panel */}
          <div className="xl:col-span-1 space-y-6">
            {/* Stream Status */}
            <div className="bg-white rounded-lg shadow-md p-4">
              <h3 className="text-lg font-semibold mb-3">Stream Status</h3>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Participants</span>
                  <span className="font-medium">{peerIds.length}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Layout</span>
                  <span className="font-medium capitalize">{layoutMode}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Local Camera</span>
                  <div
                    className={`w-3 h-3 rounded-full ${
                      localStream ? 'bg-green-500' : 'bg-red-500'
                    }`}
                  />
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Composite Output</span>
                  <div
                    className={`w-3 h-3 rounded-full ${
                      canvasStream ? 'bg-green-500' : 'bg-red-500'
                    }`}
                  />
                </div>
              </div>
            </div>

            {/* Layout Controls */}
            <div className="bg-white rounded-lg shadow-md p-4">
              <h3 className="text-lg font-semibold mb-3">Layout Controls</h3>
              <div className="grid grid-cols-2 gap-2">
                {(['grid', 'spotlight', 'sidebar', 'pip'] as LayoutMode[]).map((mode) => (
                  <button
                    key={mode}
                    onClick={() => setLayoutMode(mode)}
                    className={`px-3 py-2 text-sm rounded-md transition-colors ${
                      layoutMode === mode
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                    }`}
                  >
                    {mode.charAt(0).toUpperCase() + mode.slice(1)}
                  </button>
                ))}
              </div>
            </div>

            {/* RTMP Streaming Panel */}
            <RTMPStreaming
              canvasStream={canvasStream}
              isHost={true}
            />

            {/* Help */}
            <div className="bg-blue-50 rounded-lg p-4">
              <h4 className="font-medium text-blue-900 mb-2">Quick Start</h4>
              <ol className="text-sm text-blue-800 space-y-1">
                <li>1. Ensure participants have joined</li>
                <li>2. Check composite output is active</li>
                <li>3. Configure RTMP settings</li>
                <li>4. Start streaming</li>
              </ol>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-sm text-gray-500">
          <p>
            WebRTC-to-RTMP Streaming • Powered by Mediasoup & WebGL
          </p>
        </div>
      </div>
    </div>
  );
};
