import React, { useEffect, useRef, useState, useCallback } from 'react';
import { LogOut, Users, Monitor, Mic, MicOff, Video, VideoOff, Share, Grid, Maximize2, Copy, Check, QrCode, X } from 'lucide-react';
import { useConnection } from '../contexts/ConnectionContext';
import { VideoCompositor } from './VideoCompositor';
import { RTMPStreaming } from './RTMPStreaming';
import { useMediasoup } from '../hooks/useMediasoup';
import QRCode from 'qrcode';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

interface HostInterfaceProps {
  roomId: string;
  eventName?: string;
  onLeave: () => void;
}

type LayoutMode = 'grid' | 'focus' | 'pip';

export const HostInterface: React.FC<HostInterfaceProps> = ({ roomId, eventName, onLeave }) => {
  const { peersRef, peerIds, joinRoom, localStream, toggleAudio, toggleVideo, toggleScreenShare, isAudioEnabled, isVideoEnabled, isScreenSharing } = useConnection();
  const [hasJoined, setHasJoined] = useState(false);
  const [layoutMode, setLayoutMode] = useState<LayoutMode>('grid');
  const [canvasStream, setCanvasStream] = useState<MediaStream | null>(null);
  const [linkCopied, setLinkCopied] = useState(false);
  const [showQRCode, setShowQRCode] = useState(false);
  const [qrCodeDataUrl, setQrCodeDataUrl] = useState<string>('');
  const [showVideoOverlay, setShowVideoOverlay] = useState(false);
  const [showIconOverlay, setShowIconOverlay] = useState(true);
  const [videoOverlayOpacity, setVideoOverlayOpacity] = useState(0);
  const [iconOverlayOpacity, setIconOverlayOpacity] = useState(1);
  const localVideoRef = useRef<HTMLVideoElement>(null);
  const canvasStreamRestartRef = useRef<(() => void) | null>(null);
  const overlayVideoRef = useRef<HTMLVideoElement>(null);

  // Get streaming state for status indicator
  const { state: mediasoupState } = useMediasoup();

  useEffect(() => {
    if (!hasJoined) {
      joinRoom(roomId, true);
      setHasJoined(true);
    }
  }, [roomId, joinRoom, hasJoined]);

  useEffect(() => {
    if (localStream && localVideoRef.current) {
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);

  // Load overlay video for thumbnail
  useEffect(() => {
    if (overlayVideoRef.current) {
      overlayVideoRef.current.src = "https://www.switcherstudio.com/hubfs/Homepage%20Video%20Hero/homepage_hero_desktop_rev.webm";
      overlayVideoRef.current.load();
    }
  }, []);



  const handleCanvasStreamChange = useCallback((stream: MediaStream | null) => {
    setCanvasStream(stream);
    console.log('Canvas stream updated:', stream ? 'Available' : 'Stopped');
  }, []);

  const handleCanvasStreamRestart = useCallback((restartFn: () => void) => {
    canvasStreamRestartRef.current = restartFn;
  }, []);

  // Toggle overlay functions with fade animations
  const toggleVideoOverlay = useCallback(() => {
    const newState = !showVideoOverlay;
    setShowVideoOverlay(newState);

    // Animate opacity
    const targetOpacity = newState ? 1 : 0;
    const startOpacity = videoOverlayOpacity;
    const duration = 300;
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const currentOpacity = startOpacity + (targetOpacity - startOpacity) * progress;

      setVideoOverlayOpacity(currentOpacity);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [showVideoOverlay, videoOverlayOpacity]);

  const toggleIconOverlay = useCallback(() => {
    const newState = !showIconOverlay;
    setShowIconOverlay(newState);

    // Animate opacity
    const targetOpacity = newState ? 1 : 0;
    const startOpacity = iconOverlayOpacity;
    const duration = 300;
    const startTime = Date.now();

    const animate = () => {
      const elapsed = Date.now() - startTime;
      const progress = Math.min(elapsed / duration, 1);
      const currentOpacity = startOpacity + (targetOpacity - startOpacity) * progress;

      setIconOverlayOpacity(currentOpacity);

      if (progress < 1) {
        requestAnimationFrame(animate);
      }
    };

    requestAnimationFrame(animate);
  }, [showIconOverlay, iconOverlayOpacity]);

  const handleLeaveProduction = async () => {
    try {
      // Finish the production when host leaves
      await axios.post(`${API_BASE_URL}/productions/${roomId}/complete`);
    } catch (error) {
      console.error('Failed to finish production:', error);
    } finally {
      // Always call onLeave to return to the dashboard
      onLeave();
    }
  };

  const handleCopyLink = async () => {
    const joinUrl = `${window.location.origin}/join/${roomId}`;
    try {
      await navigator.clipboard.writeText(joinUrl);
      setLinkCopied(true);
      setTimeout(() => setLinkCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy link:', error);
    }
  };

  const handleShowQRCode = async () => {
    const joinLink = `${window.location.origin}/join/${roomId}`;
    try {
      const qrDataUrl = await QRCode.toDataURL(joinLink, {
        width: 256,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });
      setQrCodeDataUrl(qrDataUrl);
      setShowQRCode(true);
    } catch (err) {
      console.error('Failed to generate QR code:', err);
    }
  };

  return (
    <div className="min-h-screen p-2" style={{ backgroundColor: '#1a1f2e' }}>
      {/* Header */}
      <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-3 mb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-orange-500/20 p-3 rounded-lg">
              <Monitor className="w-8 h-8 text-orange-500" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                {eventName ? eventName : 'Production Center'}
              </h1>
              <div className="flex items-center space-x-2">
                <span className="text-gray-400">Share Link:</span>
                <button
                  onClick={handleCopyLink}
                  className="flex items-center space-x-2 bg-orange-500/20 hover:bg-orange-500/30 text-orange-400 px-3 py-1 rounded-lg transition-colors"
                  title={`Copy join link: ${window.location.origin}/join/${roomId}`}
                >
                  <span className="font-mono text-sm">{roomId}</span>
                  {linkCopied ? (
                    <Check className="w-4 h-4" />
                  ) : (
                    <Copy className="w-4 h-4" />
                  )}
                </button>
                <button
                  onClick={handleShowQRCode}
                  className="flex items-center space-x-1 bg-orange-500/20 hover:bg-orange-500/30 text-orange-400 px-2 py-1 rounded-lg transition-colors"
                  title="Show QR Code"
                >
                  <QrCode className="w-4 h-4" />
                </button>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleLeaveProduction}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-lg transition-colors flex items-center space-x-2"
            >
              <LogOut className="w-5 h-5" />
              <span>Finish Production</span>
            </button>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 xl:grid-cols-4 gap-2">
        {/* Main Composite View */}
        <div className="xl:col-span-3">
          <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-2">
            <div className="flex items-center justify-between mb-2">
              <h2 className="text-xl font-bold text-white">Live Composite</h2>
              <div className="flex items-center space-x-4">
                {/* Layout Controls */}
                <div className="flex space-x-2">
                  <button
                    onClick={() => setLayoutMode('grid')}
                    className={`p-2 rounded-lg transition-colors ${
                      layoutMode === 'grid' ? 'bg-blue-500/30 text-blue-400' : 'bg-gray-700/50 text-gray-400 hover:text-white'
                    }`}
                    title="Grid Layout"
                  >
                    <Grid className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setLayoutMode('focus')}
                    className={`p-2 rounded-lg transition-colors ${
                      layoutMode === 'focus' ? 'bg-blue-500/30 text-blue-400' : 'bg-gray-700/50 text-gray-400 hover:text-white'
                    }`}
                    title="Focus Layout"
                  >
                    <Monitor className="w-4 h-4" />
                  </button>
                  <button
                    onClick={() => setLayoutMode('pip')}
                    className={`p-2 rounded-lg transition-colors ${
                      layoutMode === 'pip' ? 'bg-blue-500/30 text-blue-400' : 'bg-gray-700/50 text-gray-400 hover:text-white'
                    }`}
                    title="Picture-in-Picture"
                  >
                    <Maximize2 className="w-4 h-4" />
                  </button>

                </div>

                {/* Live Composite Status Indicator */}
                <div className="flex items-center space-x-2">
                  <div className={`w-3 h-3 rounded-full ${
                    mediasoupState.isStreaming ? 'bg-red-500 animate-pulse' :
                    canvasStream ? 'bg-green-500' : 'bg-gray-500'
                  }`}></div>
                  <span className="text-gray-300">
                    {mediasoupState.isStreaming ? 'Live Composite' :
                     canvasStream ? 'Live Composite' : 'Offline'}
                  </span>
                </div>

                {/* Composite Status */}
                <div className="bg-gray-700/50 rounded-lg px-3 py-1">
                  <span className="text-white text-sm">
                    Layout: {layoutMode.toUpperCase()} • {peerIds.length} participants • {localStream ? 'Host' : 'No host'}
                  </span>
                </div>
              </div>
            </div>

            <VideoCompositor
              peers={peersRef.current || {}}
              peerIds={peerIds}
              localStream={localStream}
              overlayVideoUrl="https://www.switcherstudio.com/hubfs/Homepage%20Video%20Hero/homepage_hero_desktop_rev.webm"
              showOverlays={false}
              layoutMode={layoutMode}
              onLayoutModeChange={setLayoutMode}
              onCanvasStreamChange={handleCanvasStreamChange}
              onCanvasStreamRestart={handleCanvasStreamRestart}
              videoOverlayOpacity={videoOverlayOpacity}
              iconOverlayOpacity={iconOverlayOpacity}
            />
          </div>
        </div>

        {/* Control Panel */}
        <div className="space-y-2">
          {/* Local Camera & Controls */}
          <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-3">
            <h3 className="text-lg font-bold text-white mb-3">Local Camera</h3>

            <div className="flex items-start space-x-3">
              {/* Camera Controls */}
              <div className="flex flex-col space-y-2">
                <button
                  onClick={toggleAudio}
                  className={`p-2 rounded-lg transition-colors ${
                    isAudioEnabled ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                  }`}
                  title={isAudioEnabled ? 'Mute Audio' : 'Unmute Audio'}
                >
                  {isAudioEnabled ? <Mic className="w-4 h-4" /> : <MicOff className="w-4 h-4" />}
                </button>
                <button
                  onClick={toggleVideo}
                  className={`p-2 rounded-lg transition-colors ${
                    isVideoEnabled ? 'bg-green-500/20 text-green-400' : 'bg-red-500/20 text-red-400'
                  }`}
                  title={isVideoEnabled ? 'Turn Off Camera' : 'Turn On Camera'}
                >
                  {isVideoEnabled ? <Video className="w-4 h-4" /> : <VideoOff className="w-4 h-4" />}
                </button>
                <button
                  onClick={toggleScreenShare}
                  className={`p-2 rounded-lg transition-colors ${
                    isScreenSharing ? 'bg-blue-500/20 text-blue-400' : 'bg-gray-500/20 text-gray-400'
                  }`}
                  title={isScreenSharing ? 'Stop Screen Share' : 'Start Screen Share'}
                >
                  <Share className="w-4 h-4" />
                </button>
              </div>

              {/* Camera Thumbnail */}
              <div className="relative bg-gray-900 rounded-lg overflow-hidden w-24 h-16">
                <video
                  ref={localVideoRef}
                  autoPlay
                  muted
                  playsInline
                  className="w-full h-full object-cover"
                />
              </div>
            </div>

            {/* Overlay Controls */}
            <div className="mt-4">
              <h4 className="text-sm font-semibold text-gray-300 mb-2">Overlay Controls</h4>
              <div className="flex space-x-3">
                {/* Video Overlay Toggle */}
                <button
                  onClick={toggleVideoOverlay}
                  className={`relative group transition-all duration-200 ${
                    showVideoOverlay ? 'ring-2 ring-blue-400' : 'hover:ring-1 hover:ring-gray-400'
                  }`}
                  title="Toggle Video Overlay"
                >
                  <div className="w-16 h-12 bg-gray-900 rounded-lg overflow-hidden">
                    <video
                      ref={overlayVideoRef}
                      autoPlay
                      muted
                      playsInline
                      loop
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className={`absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center transition-opacity ${
                    showVideoOverlay ? 'opacity-0' : 'opacity-100'
                  }`}>
                    <div className="w-4 h-4 bg-white rounded-full"></div>
                  </div>
                </button>

                {/* Icon Overlay Toggle */}
                <button
                  onClick={toggleIconOverlay}
                  className={`relative group transition-all duration-200 ${
                    showIconOverlay ? 'ring-2 ring-blue-400' : 'hover:ring-1 hover:ring-gray-400'
                  }`}
                  title="Toggle Icon Overlay"
                >
                  <div className="w-16 h-12 bg-gray-900 rounded-lg overflow-hidden flex items-center justify-center">
                    <img
                      src="/switcher-logo.svg"
                      alt="Logo"
                      className="w-8 h-8 object-contain"
                    />
                  </div>
                  <div className={`absolute inset-0 bg-black/50 rounded-lg flex items-center justify-center transition-opacity ${
                    showIconOverlay ? 'opacity-0' : 'opacity-100'
                  }`}>
                    <div className="w-4 h-4 bg-white rounded-full"></div>
                  </div>
                </button>
              </div>
            </div>
          </div>

          {/* Additional Cameras - Only show if there are participants */}
          {peerIds.length > 0 && (
            <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-3">
              <h3 className="text-lg font-bold text-white mb-2">Additional Cameras</h3>
              <div className="space-y-1">
                {peerIds.map((peerId) => (
                  <div key={peerId} className="flex items-center justify-between bg-gray-700/50 rounded-lg p-2">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-purple-500 rounded-full flex items-center justify-center">
                        <Users className="w-4 h-4 text-white" />
                      </div>
                      <span className="text-white">{peerId.substring(0, 8)}...</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-2 h-2 bg-green-400 rounded-full"></div>
                      <span className="text-green-400 text-sm">Connected</span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* RTMP Streaming */}
          <RTMPStreaming
            canvasStream={canvasStream}
            isHost={true}
            onRestartCanvasStream={() => canvasStreamRestartRef.current?.()}
          />
        </div>
      </div>

      {/* QR Code Popover */}
      {showQRCode && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50" onClick={() => setShowQRCode(false)}>
          <div className="bg-gray-800 border border-gray-600 rounded-2xl p-6 max-w-sm mx-4" onClick={(e) => e.stopPropagation()}>
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-bold text-white">Join Production</h3>
              <button
                onClick={() => setShowQRCode(false)}
                className="text-gray-400 hover:text-white transition-colors"
              >
                <X className="w-5 h-5" />
              </button>
            </div>

            <div className="text-center">
              <div className="bg-white p-4 rounded-lg mb-4 inline-block">
                {qrCodeDataUrl && (
                  <img src={qrCodeDataUrl} alt="QR Code" className="w-48 h-48" />
                )}
              </div>

              <p className="text-gray-300 text-sm mb-2">
                Scan with your phone to join this production
              </p>

              <div className="bg-gray-700/50 rounded-lg p-3">
                <p className="text-white font-mono text-lg text-center">{roomId}</p>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
