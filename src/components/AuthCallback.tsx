import React, { useEffect, useState } from 'react';
import { useAuth } from '../contexts/AuthContext';

export const AuthCallback: React.FC = () => {
  const { loading, isAuthenticated, user } = useAuth();
  const [debugInfo, setDebugInfo] = useState<string>('');

  useEffect(() => {
    // Debug information
    const urlParams = new URLSearchParams(window.location.search);
    const token = urlParams.get('token');

    setDebugInfo(`
      URL: ${window.location.href}
      Token in URL: ${token ? 'Yes' : 'No'}
      Loading: ${loading}
      Authenticated: ${isAuthenticated}
      User: ${user ? user.name : 'None'}
    `);

    console.log('AuthCallback mounted:', {
      url: window.location.href,
      token,
      loading,
      isAuthenticated,
      user
    });

    // If authentication is complete, redirect to main app
    if (!loading && isAuthenticated) {
      console.log('Authentication complete, redirecting...');
      // Small delay to ensure state is updated
      setTimeout(() => {
        window.location.href = '/';
      }, 1000);
    }
  }, [loading, isAuthenticated, user]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center">
      <div className="text-center max-w-md">
        <div className="w-16 h-16 border-4 border-purple-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
        <p className="text-white text-lg mb-4">Completing authentication...</p>

        {/* Debug info in development */}
        {import.meta.env.DEV && (
          <div className="bg-gray-800 p-4 rounded-lg text-left">
            <p className="text-gray-300 text-sm font-mono whitespace-pre-line">
              {debugInfo}
            </p>
          </div>
        )}
      </div>
    </div>
  );
};
