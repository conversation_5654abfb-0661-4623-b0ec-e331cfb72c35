import React, { useEffect, useRef, useState } from 'react';
import { LogOut, Mic, MicOff, Video, VideoOff, Users, Share, Info } from 'lucide-react';
import { useConnection } from '../contexts/ConnectionContext';
import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

interface ParticipantInterfaceProps {
  roomId: string;
  eventName?: string;
  onLeave: () => void;
}

export const ParticipantInterface: React.FC<ParticipantInterfaceProps> = ({ roomId, eventName, onLeave }) => {
  const { joinRoom, localStream, toggleAudio, toggleVideo, toggleScreenShare, isAudioEnabled, isVideoEnabled, isScreenSharing, connectionStatus, connectionType } = useConnection();
  const [hasJoined, setHasJoined] = useState(false);
  const [showConnectionInfo, setShowConnectionInfo] = useState(false);
  const [fetchedEventName, setFetchedEventName] = useState<string>('');
  const localVideoRef = useRef<HTMLVideoElement>(null);

  useEffect(() => {
    if (!hasJoined) {
      joinRoom(roomId, false);
      setHasJoined(true);
    }
  }, [roomId, joinRoom, hasJoined]);

  useEffect(() => {
    if (localStream && localVideoRef.current) {
      localVideoRef.current.srcObject = localStream;
    }
  }, [localStream]);

  // Fetch event name if not provided
  useEffect(() => {
    if (!eventName && roomId) {
      const fetchEventName = async () => {
        try {
          const response = await axios.get(`${API_BASE_URL}/productions/${roomId}`);
          if (response.data?.eventName) {
            setFetchedEventName(response.data.eventName);
          }
        } catch (error) {
          console.error('Failed to fetch event name:', error);
        }
      };
      fetchEventName();
    }
  }, [roomId, eventName]);

  const getStatusColor = () => {
    switch (connectionStatus) {
      case 'connected': return 'text-green-400';
      case 'connecting': return 'text-yellow-400';
      case 'disconnected': return 'text-red-400';
      default: return 'text-gray-400';
    }
  };

  const getStatusText = () => {
    switch (connectionStatus) {
      case 'connected': return 'Connected to host';
      case 'connecting': return 'Connecting...';
      case 'disconnected': return 'Disconnected';
      default: return 'Initializing...';
    }
  };

  const getConnectionTypeText = () => {
    switch (connectionType) {
      case 'local': return 'Local Network (WiFi)';
      case 'internet': return 'Internet (STUN)';
      case 'relay': return 'TURN Server (Relay)';
      case 'unknown': return 'Detecting...';
      default: return 'Unknown';
    }
  };

  const getConnectionTypeColor = () => {
    switch (connectionType) {
      case 'local': return 'text-green-400';
      case 'internet': return 'text-blue-400';
      case 'relay': return 'text-orange-400';
      case 'unknown': return 'text-gray-400';
      default: return 'text-gray-400';
    }
  };

  const getConnectionTypeIcon = () => {
    switch (connectionType) {
      case 'local': return '📶'; // WiFi signal
      case 'internet': return '🌐'; // Globe for internet
      case 'relay': return '🔄'; // Arrows for relay
      case 'unknown': return '❓'; // Question mark
      default: return '❓';
    }
  };

  const getConnectionTypeDescription = () => {
    switch (connectionType) {
      case 'local':
        return 'Direct connection over your local network (WiFi/Ethernet). This provides the best quality and lowest latency.';
      case 'internet':
        return 'Connection through the internet using STUN servers. Good quality with moderate latency.';
      case 'relay':
        return 'Connection through a TURN relay server. This may have higher latency but ensures connectivity in restrictive networks.';
      case 'unknown':
        return 'Connection type is being detected...';
      default:
        return 'Connection type could not be determined.';
    }
  };

  return (
    <div className="min-h-screen p-2" style={{ backgroundColor: '#1a1f2e' }}>
      {/* Header */}
      <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-3 mb-2">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="bg-orange-500/20 p-3 rounded-lg">
              <Users className="w-8 h-8 text-orange-500" />
            </div>
            <div>
              <h1 className="text-2xl font-bold text-white">
                {eventName || fetchedEventName || 'Participant View'}
              </h1>
              <p className="text-gray-300">Room ID: <span className="font-mono text-orange-400">{roomId}</span></p>
            </div>
          </div>
          <div className="flex items-center space-x-3">
            <div className={`flex items-center space-x-2 bg-gray-700/50 rounded-lg px-3 py-2`}>
              <div className={`w-2 h-2 rounded-full ${connectionStatus === 'connected' ? 'bg-green-400' : connectionStatus === 'connecting' ? 'bg-yellow-400 animate-pulse' : 'bg-red-400'}`}></div>
              <span className={`font-semibold ${getStatusColor()}`}>{getStatusText()}</span>
            </div>
            {connectionStatus === 'connected' && (
              <div className="relative">
                <div className={`flex items-center space-x-2 bg-gray-700/50 rounded-lg px-3 py-2`}>
                  <span className="text-lg">{getConnectionTypeIcon()}</span>
                  <span className={`font-medium ${getConnectionTypeColor()}`}>{getConnectionTypeText()}</span>
                  <button
                    onClick={() => setShowConnectionInfo(!showConnectionInfo)}
                    className="ml-2 text-gray-400 hover:text-white transition-colors"
                    title="Connection details"
                  >
                    <Info className="w-4 h-4" />
                  </button>
                </div>
                {showConnectionInfo && (
                  <div className="absolute top-full mt-2 right-0 bg-gray-800 border border-gray-600 rounded-lg p-3 w-80 z-10 shadow-lg">
                    <div className="text-sm text-gray-300">
                      <div className="flex items-center space-x-2 mb-2">
                        <span className="text-lg">{getConnectionTypeIcon()}</span>
                        <span className={`font-semibold ${getConnectionTypeColor()}`}>{getConnectionTypeText()}</span>
                      </div>
                      <p className="text-gray-400 leading-relaxed">
                        {getConnectionTypeDescription()}
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}
            <button
              onClick={onLeave}
              className="bg-red-500 hover:bg-red-600 text-white px-4 py-3 rounded-lg transition-colors flex items-center space-x-2"
            >
              <LogOut className="w-5 h-5" />
              <span>Leave</span>
            </button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto">
        <div className="bg-gray-800/80 backdrop-blur-lg border border-gray-600/50 rounded-2xl p-6">
          {/* Video Preview */}
          <div className="relative bg-gray-900 rounded-2xl overflow-hidden aspect-video max-w-2xl mx-auto mb-6">
            <video
              ref={localVideoRef}
              autoPlay
              muted
              playsInline
              className="w-full h-full object-cover"
            />
            <div className="absolute top-4 left-4">
              <div className="bg-red-500/20 backdrop-blur-sm border border-red-500/30 rounded-lg px-3 py-1">
                <span className="text-red-400 font-semibold text-sm">LIVE</span>
              </div>
            </div>
            <div className="absolute bottom-4 left-4 flex space-x-2">
              <button
                onClick={toggleAudio}
                className={`p-3 rounded-lg transition-all duration-200 backdrop-blur-sm ${
                  isAudioEnabled
                    ? 'bg-green-500/20 border border-green-500/30 text-green-400 hover:bg-green-500/30'
                    : 'bg-red-500/20 border border-red-500/30 text-red-400 hover:bg-red-500/30'
                }`}
              >
                {isAudioEnabled ? <Mic className="w-5 h-5" /> : <MicOff className="w-5 h-5" />}
              </button>
              <button
                onClick={toggleVideo}
                className={`p-3 rounded-lg transition-all duration-200 backdrop-blur-sm ${
                  isVideoEnabled
                    ? 'bg-green-500/20 border border-green-500/30 text-green-400 hover:bg-green-500/30'
                    : 'bg-red-500/20 border border-red-500/30 text-red-400 hover:bg-red-500/30'
                }`}
              >
                {isVideoEnabled ? <Video className="w-5 h-5" /> : <VideoOff className="w-5 h-5" />}
              </button>
              <button
                onClick={toggleScreenShare}
                className={`p-3 rounded-lg transition-all duration-200 backdrop-blur-sm ${
                  isScreenSharing
                    ? 'bg-blue-500/20 border border-blue-500/30 text-blue-400 hover:bg-blue-500/30'
                    : 'bg-gray-500/20 border border-gray-500/30 text-gray-400 hover:bg-gray-500/30'
                }`}
                title={isScreenSharing ? 'Stop Screen Share' : 'Start Screen Share'}
              >
                <Share className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Controls Info */}
          <div className="bg-gray-700/30 backdrop-blur-sm border border-gray-600 rounded-xl p-6">
            <h3 className="text-lg font-semibold text-white mb-4">Stream Controls</h3>
            <div className="grid md:grid-cols-2 gap-4 text-gray-300">
              <div className="flex items-center space-x-3">
                {isAudioEnabled ? <Mic className="w-5 h-5 text-green-400" /> : <MicOff className="w-5 h-5 text-red-400" />}
                <span>Microphone: {isAudioEnabled ? 'On' : 'Off'}</span>
              </div>
              <div className="flex items-center space-x-3">
                {isVideoEnabled ? <Video className="w-5 h-5 text-green-400" /> : <VideoOff className="w-5 h-5 text-red-400" />}
                <span>Camera: {isVideoEnabled ? 'On' : 'Off'}</span>
              </div>
              <div className="flex items-center space-x-3">
                <Share className={`w-5 h-5 ${isScreenSharing ? 'text-blue-400' : 'text-gray-400'}`} />
                <span>Screen Share: {isScreenSharing ? 'Active' : 'Off'}</span>
              </div>
              <div className="flex items-center space-x-3">
                <span className="text-lg">{getConnectionTypeIcon()}</span>
                <span className={getConnectionTypeColor()}>Connection: {getConnectionTypeText()}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};