import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import axios from 'axios';

interface User {
  id: string;
  email: string;
  name: string;
  avatar?: string;
  currentRoomId?: string;
  provider: string;
}

interface AuthContextType {
  user: User | null;
  token: string | null;
  login: (email: string, password: string) => Promise<void>;
  register: (email: string, password: string, name: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
  isAuthenticated: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(localStorage.getItem('auth_token'));
  const [loading, setLoading] = useState(true);

  // Configure axios defaults
  useEffect(() => {
    if (token) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${token}`;
    } else {
      delete axios.defaults.headers.common['Authorization'];
    }
  }, [token]);

  // Load user profile on mount if token exists
  useEffect(() => {
    const loadUser = async () => {
      console.log('Loading user with token:', token ? 'exists' : 'none');
      if (token) {
        try {
          console.log('Fetching user profile...');
          const response = await axios.get(`${API_BASE_URL}/auth/profile`);
          console.log('User profile loaded:', response.data);
          setUser(response.data);
        } catch (error) {
          console.error('Failed to load user profile:', error);
          // Token might be invalid, clear it
          localStorage.removeItem('auth_token');
          setToken(null);
        }
      }
      console.log('Setting loading to false');
      setLoading(false);
    };

    loadUser();
  }, [token]);

  // Handle OAuth callback
  useEffect(() => {
    const handleOAuthCallback = () => {
      const urlParams = new URLSearchParams(window.location.search);
      const tokenFromUrl = urlParams.get('token');

      console.log('OAuth callback check:', {
        pathname: window.location.pathname,
        search: window.location.search,
        tokenFromUrl
      });

      if (tokenFromUrl) {
        console.log('Setting token from OAuth callback:', tokenFromUrl);
        localStorage.setItem('auth_token', tokenFromUrl);
        setToken(tokenFromUrl);
        // Clear the token from URL and redirect to home
        window.history.replaceState({}, document.title, '/');
      }
    };

    // Check for OAuth callback on any page with token parameter
    const urlParams = new URLSearchParams(window.location.search);
    if (urlParams.get('token') || window.location.pathname === '/auth/callback') {
      handleOAuthCallback();
    }
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/login`, {
        email,
        password,
      });

      const { access_token, user: userData } = response.data;
      localStorage.setItem('auth_token', access_token);
      setToken(access_token);
      setUser(userData);
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  };

  const register = async (email: string, password: string, name: string) => {
    try {
      const response = await axios.post(`${API_BASE_URL}/auth/register`, {
        email,
        password,
        name,
      });

      const { access_token, user: userData } = response.data;
      localStorage.setItem('auth_token', access_token);
      setToken(access_token);
      setUser(userData);
    } catch (error) {
      console.error('Registration failed:', error);
      throw error;
    }
  };

  const logout = () => {
    localStorage.removeItem('auth_token');
    setToken(null);
    setUser(null);
    delete axios.defaults.headers.common['Authorization'];
  };

  const value: AuthContextType = {
    user,
    token,
    login,
    register,
    logout,
    loading,
    isAuthenticated: !!user,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
