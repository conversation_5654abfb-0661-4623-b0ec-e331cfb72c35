import React, { useState } from 'react';
import { LoginScreen } from './components/LoginScreen';
import { RoomManager } from './components/RoomManager';
import { HostInterface } from './components/HostInterface';
import { ParticipantInterface } from './components/ParticipantInterface';
import { AuthCallback } from './components/AuthCallback';
import { AuthProvider, useAuth } from './contexts/AuthContext';
import { ConnectionProvider, useConnection } from './contexts/ConnectionContext';

type AppMode = 'productions' | 'host' | 'participant';

const AppContent: React.FC = () => {
  const { isAuthenticated, loading } = useAuth();
  const [mode, setMode] = useState<AppMode>('productions');
  const [productionId, setProductionId] = useState<string>('');
  const [eventName, setEventName] = useState<string>('');
  const { leaveRoom } = useConnection();

  const handleJoinAsHost = (id: string, name?: string) => {
    setProductionId(id);
    setEventName(name || '');
    setMode('host');
  };

  const handleJoinAsParticipant = (id: string) => {
    setProductionId(id);
    setEventName(''); // Participants don't get event name initially
    setMode('participant');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900 flex items-center justify-center">
        <div className="w-12 h-12 border-4 border-purple-500 border-t-transparent rounded-full animate-spin"></div>
      </div>
    );
  }

  // Handle OAuth callback route or token in URL
  const urlParams = new URLSearchParams(window.location.search);
  if (window.location.pathname === '/auth/callback' || urlParams.get('token')) {
    return <AuthCallback />;
  }

  // Check for join production in URL path
  const pathMatch = window.location.pathname.match(/^\/join\/(.+)$/);
  const joinProductionId = pathMatch ? pathMatch[1] : null;

  if (!isAuthenticated) {
    return <LoginScreen onJoinAsParticipant={handleJoinAsParticipant} />;
  }

  // If authenticated and there's a join parameter, join as participant
  if (joinProductionId && mode === 'productions') {
    handleJoinAsParticipant(joinProductionId);
  }

  const handleLeaveProduction = () => {
    // First properly leave the production (disconnect socket, clean up peers, stop streams)
    leaveRoom();
    // Then switch back to production manager
    setMode('productions');
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-900 via-purple-900 to-gray-900">
      {mode === 'productions' && (
        <RoomManager
          onJoinAsHost={handleJoinAsHost}
        />
      )}
      {mode === 'host' && (
        <HostInterface roomId={productionId} eventName={eventName} onLeave={handleLeaveProduction} />
      )}
      {mode === 'participant' && (
        <ParticipantInterface roomId={productionId} eventName={eventName} onLeave={handleLeaveProduction} />
      )}
    </div>
  );
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <ConnectionProvider>
        <AppContent />
      </ConnectionProvider>
    </AuthProvider>
  );
};

export default App;