{"version": 4, "terraform_version": "1.12.2", "serial": 349, "lineage": "857bdcf3-f01d-2560-9f6e-b67344701c42", "outputs": {"deployment_commands": {"value": {"configure_turn": "export VITE_TURN_SERVER=************ && export VITE_TURN_USERNAME=webrtc", "media_server": "gcloud compute ssh webrtc-platform-dev-media-server --zone=us-central1-a --command='sudo systemctl restart media-server'", "signaling_server": "gcloud run deploy webrtc-signaling --image us-central1-docker.pkg.dev/switcher-studio-233517/webrtc-platform-dev-repo/webrtc-signaling:latest --region us-central1"}, "type": ["object", {"configure_turn": "string", "media_server": "string", "signaling_server": "string"}], "sensitive": true}, "environment_config": {"value": {"artifact_registry": "us-central1-docker.pkg.dev/switcher-studio-233517/webrtc-platform-dev-repo", "environment": "dev", "media_server_url": "https://*************:8080", "project_id": "switcher-studio-233517", "region": "us-central1", "signaling_server_url": "https://webrtc-platform-dev-signaling-7egrqrsfva-uc.a.run.app", "turn_server_ip": "************", "turn_username": "webrtc"}, "type": ["object", {"artifact_registry": "string", "environment": "string", "media_server_url": "string", "project_id": "string", "region": "string", "signaling_server_url": "string", "turn_server_ip": "string", "turn_username": "string"}], "sensitive": true}, "media_server_url": {"value": "https://*************:8080", "type": "string"}, "signaling_server_url": {"value": "https://webrtc-platform-dev-signaling-7egrqrsfva-uc.a.run.app", "type": "string"}, "turn_server_ip": {"value": "************", "type": "string"}}, "resources": [{"module": "module.webrtc_platform", "mode": "managed", "type": "google_project_service", "name": "required_apis", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"index_key": "artifactregistry.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/artifactregistry.googleapis.com", "project": "switcher-studio-233517", "service": "artifactregistry.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "create_before_destroy": true}, {"index_key": "cloudbuild.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/cloudbuild.googleapis.com", "project": "switcher-studio-233517", "service": "cloudbuild.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "create_before_destroy": true}, {"index_key": "compute.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/compute.googleapis.com", "project": "switcher-studio-233517", "service": "compute.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "create_before_destroy": true}, {"index_key": "dns.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/dns.googleapis.com", "project": "switcher-studio-233517", "service": "dns.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "create_before_destroy": true}, {"index_key": "logging.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/logging.googleapis.com", "project": "switcher-studio-233517", "service": "logging.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "create_before_destroy": true}, {"index_key": "monitoring.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/monitoring.googleapis.com", "project": "switcher-studio-233517", "service": "monitoring.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "create_before_destroy": true}, {"index_key": "run.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/run.googleapis.com", "project": "switcher-studio-233517", "service": "run.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "create_before_destroy": true}, {"index_key": "secretmanager.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/secretmanager.googleapis.com", "project": "switcher-studio-233517", "service": "secretmanager.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "create_before_destroy": true}, {"index_key": "vpcaccess.googleapis.com", "schema_version": 0, "attributes": {"disable_dependent_services": false, "disable_on_destroy": false, "id": "switcher-studio-233517/vpcaccess.googleapis.com", "project": "switcher-studio-233517", "service": "vpcaccess.googleapis.com", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.compute", "mode": "managed", "type": "google_compute_address", "name": "turn_server_ip", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"address": "************", "address_type": "EXTERNAL", "creation_timestamp": "2025-08-21T11:25:27.906-07:00", "description": "", "effective_labels": {}, "id": "projects/switcher-studio-233517/regions/us-central1/addresses/webrtc-platform-dev-turn-ip", "ip_version": "", "ipv6_endpoint_type": "", "label_fingerprint": "42WmSpB8rSM=", "labels": {}, "name": "webrtc-platform-dev-turn-ip", "network": "", "network_tier": "PREMIUM", "prefix_length": 0, "project": "switcher-studio-233517", "purpose": "", "region": "us-central1", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/regions/us-central1/addresses/webrtc-platform-dev-turn-ip", "subnetwork": "", "terraform_labels": {}, "timeouts": null, "users": ["https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/instances/webrtc-platform-dev-turn-server"]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.compute", "mode": "managed", "type": "google_compute_health_check", "name": "turn_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"check_interval_sec": 30, "creation_timestamp": "2025-08-21T11:25:27.279-07:00", "description": "", "grpc_health_check": [], "healthy_threshold": 2, "http2_health_check": [], "http_health_check": [], "https_health_check": [], "id": "projects/switcher-studio-233517/global/healthChecks/webrtc-platform-dev-turn-health-check", "log_config": [{"enable": false}], "name": "webrtc-platform-dev-turn-health-check", "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/healthChecks/webrtc-platform-dev-turn-health-check", "source_regions": [], "ssl_health_check": [], "tcp_health_check": [{"port": 3478, "port_name": "", "port_specification": "", "proxy_header": "NONE", "request": "", "response": ""}], "timeout_sec": 10, "timeouts": null, "type": "TCP", "unhealthy_threshold": 2}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"]}]}, {"module": "module.webrtc_platform.module.compute", "mode": "managed", "type": "google_compute_instance", "name": "turn_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 6, "attributes": {"advanced_machine_features": [], "allow_stopping_for_update": null, "attached_disk": [], "boot_disk": [{"auto_delete": true, "device_name": "persistent-disk-0", "disk_encryption_key_raw": "", "disk_encryption_key_sha256": "", "initialize_params": [{"enable_confidential_compute": false, "image": "https://www.googleapis.com/compute/v1/projects/cos-cloud/global/images/cos-stable-121-18867-199-38", "labels": {}, "provisioned_iops": 0, "provisioned_throughput": 0, "resource_manager_tags": {}, "size": 20, "storage_pool": "", "type": "pd-standard"}], "kms_key_self_link": "", "mode": "READ_WRITE", "source": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/disks/webrtc-platform-dev-turn-server"}], "can_ip_forward": false, "confidential_instance_config": [], "cpu_platform": "Intel Broadwell", "current_status": "RUNNING", "deletion_protection": false, "description": "", "desired_status": null, "effective_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "enable_display": false, "guest_accelerator": [], "hostname": "", "id": "projects/switcher-studio-233517/zones/us-central1-a/instances/webrtc-platform-dev-turn-server", "instance_id": "9193571074318195244", "label_fingerprint": "y1DYWkiuggQ=", "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "machine_type": "e2-micro", "metadata": {"enable-oslogin": "TRUE", "startup-script": "#!/bin/bash\n\n# Startup script for TURN server instance\nset -e\n\n# Configure logging\nexec > >(tee /var/log/startup-script.log)\nexec 2>&1\n\necho \"Starting TURN server setup...\"\n\n# Install required packages\napt-get update\napt-get install -y docker.io\n\n# Start Docker service\nsystemctl start docker\nsystemctl enable docker\n\n# Configure Docker authentication for Artifact Registry\ngcloud auth configure-docker --quiet\n\n# Get TURN password from Secret Manager\nTURN_PASSWORD=$(gcloud secrets versions access latest --secret=\"webrtc-platform-dev-turn-password\" --project=\"switcher-studio-233517\")\n\n# Stop any existing coturn container\ndocker stop coturn-server 2>/dev/null || true\ndocker rm coturn-server 2>/dev/null || true\n\n# Create coturn configuration directory\nmkdir -p /etc/coturn\n\n# Create coturn configuration file\ncat > /etc/coturn/turnserver.conf << EOF\n# coturn TURN server configuration for WebRTC\n\n# Listening port for TURN/STUN\nlistening-port=3478\n\n# TLS listening port (optional, for secure connections)\ntls-listening-port=5349\n\n# Relay ports range (for media)\nmin-port=49152\nmax-port=49252\n\n# Enable verbose logging\nverbose\n\n# Log file (will go to stdout in Docker)\nlog-file=stdout\n\n# Realm for authentication\nrealm=webrtc.local\n\n# Server name\nserver-name=turn-server\n\n# Authentication\n# Use long-term credentials\nlt-cred-mech\n\n# Static user credentials\nuser=webrtc:$TURN_PASSWORD\n\n# Allow loopback peers (for local testing)\nallow-loopback-peers\n\n# Disable RFC5780 support (can cause issues)\nno-rfc5780\n\n# Enable STUN\nstun-only=false\n\n# Disable software attributes\nno-software-attribute\nEOF\n\n# Pull and run coturn container\necho \"Pulling TURN server image...\"\ndocker pull coturn/coturn:latest\n\necho \"Starting TURN server container...\"\ndocker run -d \\\n    --name coturn-server \\\n    --restart unless-stopped \\\n    --network host \\\n    -v /etc/coturn/turnserver.conf:/etc/coturn/turnserver.conf:ro \\\n    -e TURN_USERNAME=webrtc \\\n    -e TURN_PASSWORD=$TURN_PASSWORD \\\n    coturn/coturn:latest \\\n    turnserver -c /etc/coturn/turnserver.conf -v\n\necho \"TURN server setup completed successfully\"\n\n# Verify the container is running\nsleep 5\nif docker ps | grep -q coturn-server; then\n    echo \"TURN server is running successfully\"\nelse\n    echo \"ERROR: TURN server failed to start\"\n    docker logs coturn-server\n    exit 1\nfi\n"}, "metadata_fingerprint": "pOHUn3CuF-k=", "metadata_startup_script": null, "min_cpu_platform": "", "name": "webrtc-platform-dev-turn-server", "network_interface": [{"access_config": [{"nat_ip": "************", "network_tier": "PREMIUM", "public_ptr_domain_name": ""}], "alias_ip_range": [], "internal_ipv6_prefix_length": 0, "ipv6_access_config": [], "ipv6_access_type": "", "ipv6_address": "", "name": "nic0", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "network_ip": "********", "nic_type": "", "queue_count": 0, "stack_type": "IPV4_ONLY", "subnetwork": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/regions/us-central1/subnetworks/webrtc-platform-dev-subnet", "subnetwork_project": "switcher-studio-233517"}], "network_performance_config": [], "params": [], "project": "switcher-studio-233517", "reservation_affinity": [], "resource_policies": [], "scheduling": [{"automatic_restart": true, "instance_termination_action": "", "local_ssd_recovery_timeout": [], "max_run_duration": [], "min_node_cpus": 0, "node_affinities": [], "on_host_maintenance": "MIGRATE", "on_instance_stop_action": [], "preemptible": false, "provisioning_model": "STANDARD"}], "scratch_disk": [], "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/instances/webrtc-platform-dev-turn-server", "service_account": [{"email": "<EMAIL>", "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}], "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false, "enable_vtpm": true}], "tags": ["ssh-server", "turn-server"], "tags_fingerprint": "FH5JVhyFy0A=", "terraform_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "timeouts": null, "zone": "us-central1-a"}, "sensitive_attributes": [[{"type": "get_attr", "value": "boot_disk"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "disk_encryption_key_raw"}], [{"type": "get_attr", "value": "metadata"}, {"type": "index", "value": {"value": "startup-script", "type": "string"}}]], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.compute.google_compute_address.turn_server_ip", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.media_server", "mode": "managed", "type": "google_compute_address", "name": "media_server_ip", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"address": "*************", "address_type": "EXTERNAL", "creation_timestamp": "2025-08-21T12:57:20.424-07:00", "description": "", "effective_labels": {}, "id": "projects/switcher-studio-233517/regions/us-central1/addresses/webrtc-platform-dev-media-ip", "ip_version": "", "ipv6_endpoint_type": "", "label_fingerprint": "42WmSpB8rSM=", "labels": {}, "name": "webrtc-platform-dev-media-ip", "network": "", "network_tier": "PREMIUM", "prefix_length": 0, "project": "switcher-studio-233517", "purpose": "", "region": "us-central1", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/regions/us-central1/addresses/webrtc-platform-dev-media-ip", "subnetwork": "", "terraform_labels": {}, "timeouts": null, "users": ["https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/instances/webrtc-platform-dev-media-server"]}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.media_server", "mode": "managed", "type": "google_compute_firewall", "name": "allow_media_server_https", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": ["8080", "443"], "protocol": "tcp"}], "creation_timestamp": "2025-08-21T12:57:19.744-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-allow-media-server-https", "log_config": [], "name": "webrtc-platform-dev-allow-media-server-https", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-allow-media-server-https", "source_ranges": ["0.0.0.0/0"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": ["media-server"], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"]}]}, {"module": "module.webrtc_platform.module.media_server", "mode": "managed", "type": "google_compute_firewall", "name": "allow_webrtc_media", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": ["32256-65535"], "protocol": "tcp"}, {"ports": ["32256-65535"], "protocol": "udp"}], "creation_timestamp": "2025-08-21T12:57:19.760-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-allow-webrtc-media", "log_config": [], "name": "webrtc-platform-dev-allow-webrtc-media", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-allow-webrtc-media", "source_ranges": ["0.0.0.0/0"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": ["media-server"], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"]}]}, {"module": "module.webrtc_platform.module.media_server", "mode": "managed", "type": "google_compute_instance", "name": "media_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 6, "attributes": {"advanced_machine_features": [], "allow_stopping_for_update": null, "attached_disk": [], "boot_disk": [{"auto_delete": true, "device_name": "persistent-disk-0", "disk_encryption_key_raw": "", "disk_encryption_key_sha256": "", "initialize_params": [{"enable_confidential_compute": false, "image": "https://www.googleapis.com/compute/v1/projects/ubuntu-os-cloud/global/images/ubuntu-2204-jammy-v20250815", "labels": {}, "provisioned_iops": 0, "provisioned_throughput": 0, "resource_manager_tags": {}, "size": 30, "storage_pool": "", "type": "pd-standard"}], "kms_key_self_link": "", "mode": "READ_WRITE", "source": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/disks/webrtc-platform-dev-media-server"}], "can_ip_forward": false, "confidential_instance_config": [], "cpu_platform": "Intel Broadwell", "current_status": "RUNNING", "deletion_protection": false, "description": "", "desired_status": null, "effective_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "enable_display": false, "guest_accelerator": [], "hostname": "", "id": "projects/switcher-studio-233517/zones/us-central1-a/instances/webrtc-platform-dev-media-server", "instance_id": "5701654450969133004", "label_fingerprint": "y1DYWkiuggQ=", "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "machine_type": "e2-standard-2", "metadata": {"enable-oslogin": "TRUE", "startup-script": "#!/bin/bash\n\n# Startup script for media server VM - npm package installation\nset -e\n\n# Configure logging\nexec > >(tee /var/log/startup-script.log)\nexec 2>&1\n\necho \"Starting media server setup via npm package...\"\n\n# Clean up any corrupted apt preferences\nrm -f /etc/apt/preferences.d/nodejs /etc/apt/preferences.d/nsolid\n\n# Update package lists and install required packages\napt-get update\napt-get install -y curl wget gnupg2 software-properties-common openssl\n\n# Clean install of Node.js 20 (required for mediasoup)\ncurl -fsSL https://deb.nodesource.com/setup_20.x | bash -\napt-get install -y nodejs\n\n# Verify Node.js is working\nif ! command -v node &> /dev/null; then\n    echo \"Node.js installation failed, trying alternative method...\"\n    # Alternative: Install from snap\n    apt-get install -y snapd\n    snap install node --classic\nfi\n\n# Verify installation\nif command -v node &> /dev/null; then\n    echo \"✅ Node.js version: $(node --version)\"\n    echo \"✅ NPM version: $(npm --version)\"\nelse\n    echo \"❌ Node.js installation failed\"\n    exit 1\nfi\n\n# Create media server directory\nmkdir -p /opt/media-server\ncd /opt/media-server\n\n# Create SSL certificates directory\nmkdir -p /opt/media-server/ssl\n\n# Generate self-signed SSL certificate for HTTPS\nopenssl req -x509 -newkey rsa:4096 -keyout /opt/media-server/ssl/key.pem -out /opt/media-server/ssl/cert.pem -days 365 -nodes -subj \"/C=US/ST=CA/L=SF/O=WebRTC/CN=*************\"\n\n# Set proper permissions for SSL certificates\nchmod 644 /opt/media-server/ssl/cert.pem\nchmod 600 /opt/media-server/ssl/key.pem\n\n# Create package.json for the deployment\ncat > package.json << EOF\n{\n  \"name\": \"media-server-deployment\",\n  \"version\": \"1.0.0\",\n  \"description\": \"Media server deployment\",\n  \"main\": \"index.js\",\n  \"scripts\": {\n    \"start\": \"webrtc-media-server\"\n  },\n  \"dependencies\": {\n    \"@webrtc-platform/media-server\": \"1.0.0\"\n  }\n}\nEOF\n\n# Configure npm to use private Artifact Registry\necho \"Configuring npm for private registry...\"\nnpm config set @webrtc-platform:registry https://us-central1-npm.pkg.dev/switcher-studio-233517/webrtc-platform-dev-npm/\n\n# Create .npmrc with authentication token\necho \"Setting up npm authentication...\"\nNPM_TOKEN=$(gcloud auth print-access-token)\ncat > .npmrc << 'EOF'\n@webrtc-platform:registry=https://us-central1-npm.pkg.dev/switcher-studio-233517/webrtc-platform-dev-npm/\nEOF\necho \"//us-central1-npm.pkg.dev/switcher-studio-233517/webrtc-platform-dev-npm/:_authToken=$NPM_TOKEN\" >> .npmrc\n\n# Install the media server package directly from npm registry\necho \"Installing media server package: @webrtc-platform/media-server@1.0.0\"\nif npm install @webrtc-platform/media-server@1.0.0; then\n    echo \"✅ Package installed successfully\"\nelse\n    echo \"❌ Package installation failed. Trying to install from public npm...\"\n    # Fallback: try to install a basic express server if private package fails\n    npm install express socket.io cors\n    echo \"⚠️  Installed fallback packages. You'll need to publish the private package.\"\nfi\n\n# Create a symlink for the binary\nln -sf /opt/media-server/node_modules/@webrtc-platform/media-server/server.js /usr/local/bin/webrtc-media-server\nchmod +x /usr/local/bin/webrtc-media-server\n\n# Create systemd service for auto-start\ncat > /etc/systemd/system/media-server.service << EOF\n[Unit]\nDescription=WebRTC Media Server\nAfter=network.target\n\n[Service]\nType=simple\nUser=root\nWorkingDirectory=/opt/media-server\nEnvironment=NODE_ENV=production\nEnvironment=LISTEN_IP=0.0.0.0\nEnvironment=ANNOUNCED_IP=*************\nEnvironment=RECORDING_IP=0.0.0.0\nEnvironment=SSL_CERT_PATH=/opt/media-server/ssl/cert.pem\nEnvironment=SSL_KEY_PATH=/opt/media-server/ssl/key.pem\nEnvironment=ENABLE_HTTPS=true\nEnvironment=PORT=8080\nEnvironment=WEBRTC_ENABLE_UDP=true\nEnvironment=WEBRTC_ENABLE_TCP=true\nEnvironment=WEBRTC_PREFER_UDP=true\nEnvironment=WEBRTC_PORT_RANGE_MIN=40000\nEnvironment=WEBRTC_PORT_RANGE_MAX=40100\nExecStart=/usr/bin/node /opt/media-server/node_modules/@webrtc-platform/media-server/server.js\nRestart=always\nRestartSec=10\n\n[Install]\nWantedBy=multi-user.target\nEOF\n\n# Enable and start the service\nsystemctl daemon-reload\nsystemctl enable media-server\nsystemctl start media-server\n\n# Wait a moment and check status\nsleep 5\nsystemctl status media-server\n\necho \"Media server setup completed successfully\"\necho \"Service status:\"\nsystemctl is-active media-server\necho \"Logs:\"\njournalctl -u media-server --no-pager --lines=10\n"}, "metadata_fingerprint": "OZIttOWf5Xo=", "metadata_startup_script": null, "min_cpu_platform": "", "name": "webrtc-platform-dev-media-server", "network_interface": [{"access_config": [{"nat_ip": "*************", "network_tier": "PREMIUM", "public_ptr_domain_name": ""}], "alias_ip_range": [], "internal_ipv6_prefix_length": 0, "ipv6_access_config": [], "ipv6_access_type": "", "ipv6_address": "", "name": "nic0", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "network_ip": "********2", "nic_type": "", "queue_count": 0, "stack_type": "IPV4_ONLY", "subnetwork": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/regions/us-central1/subnetworks/webrtc-platform-dev-subnet", "subnetwork_project": "switcher-studio-233517"}], "network_performance_config": [], "params": [], "project": "switcher-studio-233517", "reservation_affinity": [], "resource_policies": [], "scheduling": [{"automatic_restart": true, "instance_termination_action": "", "local_ssd_recovery_timeout": [], "max_run_duration": [], "min_node_cpus": 0, "node_affinities": [], "on_host_maintenance": "MIGRATE", "on_instance_stop_action": [], "preemptible": false, "provisioning_model": "STANDARD"}], "scratch_disk": [], "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/zones/us-central1-a/instances/webrtc-platform-dev-media-server", "service_account": [{"email": "<EMAIL>", "scopes": ["https://www.googleapis.com/auth/cloud-platform"]}], "shielded_instance_config": [{"enable_integrity_monitoring": true, "enable_secure_boot": false, "enable_vtpm": true}], "tags": ["http-server", "https-server", "media-server", "ssh-server"], "tags_fingerprint": "5-v9p7SR5wE=", "terraform_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "timeouts": null, "zone": "us-central1-a"}, "sensitive_attributes": [[{"type": "get_attr", "value": "boot_disk"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "disk_encryption_key_raw"}]], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.media_server.google_compute_address.media_server_ip", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_firewall", "name": "allow_health_check", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": [], "protocol": "tcp"}], "creation_timestamp": "2025-08-21T11:19:49.817-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-health-check", "log_config": [], "name": "webrtc-platform-dev-vpc-allow-health-check", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-health-check", "source_ranges": ["***********/22", "**********/16"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": ["http-server"], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_firewall", "name": "allow_http_https", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": ["80", "443"], "protocol": "tcp"}], "creation_timestamp": "2025-08-21T11:19:50.707-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-http-https", "log_config": [], "name": "webrtc-platform-dev-vpc-allow-http-https", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-http-https", "source_ranges": ["0.0.0.0/0"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": ["http-server", "https-server"], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_firewall", "name": "allow_internal", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": [], "protocol": "icmp"}, {"ports": [], "protocol": "tcp"}, {"ports": [], "protocol": "udp"}], "creation_timestamp": "2025-08-21T11:19:46.924-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-internal", "log_config": [], "name": "webrtc-platform-dev-vpc-allow-internal", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-internal", "source_ranges": ["10.0.0.0/24"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": [], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_firewall", "name": "allow_ssh", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": ["22"], "protocol": "tcp"}], "creation_timestamp": "2025-08-21T11:19:49.776-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-ssh", "log_config": [], "name": "webrtc-platform-dev-vpc-allow-ssh", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-ssh", "source_ranges": ["0.0.0.0/0"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": ["ssh-server"], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_firewall", "name": "allow_turn_relay", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": ["49152-49252"], "protocol": "udp"}], "creation_timestamp": "2025-08-21T11:19:48.614-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-turn-relay", "log_config": [], "name": "webrtc-platform-dev-vpc-allow-turn-relay", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-turn-relay", "source_ranges": ["0.0.0.0/0"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": ["turn-server"], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_firewall", "name": "allow_turn_stun", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 1, "attributes": {"allow": [{"ports": ["3478", "5349"], "protocol": "tcp"}, {"ports": ["3478", "5349"], "protocol": "udp"}], "creation_timestamp": "2025-08-21T11:19:48.095-07:00", "deny": [], "description": "", "destination_ranges": [], "direction": "INGRESS", "disabled": false, "enable_logging": null, "id": "projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-turn-stun", "log_config": [], "name": "webrtc-platform-dev-vpc-allow-turn-stun", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "priority": 1000, "project": "switcher-studio-233517", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/firewalls/webrtc-platform-dev-vpc-allow-turn-stun", "source_ranges": ["0.0.0.0/0"], "source_service_accounts": [], "source_tags": [], "target_service_accounts": [], "target_tags": ["turn-server"], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_network", "name": "vpc", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"auto_create_subnetworks": false, "delete_default_routes_on_create": false, "description": "", "enable_ula_internal_ipv6": false, "gateway_ipv4": "", "id": "projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "internal_ipv6_range": "", "mtu": 0, "name": "webrtc-platform-dev-vpc", "network_firewall_policy_enforcement_order": "AFTER_CLASSIC_FIREWALL", "numeric_id": "3887245525363908042", "project": "switcher-studio-233517", "routing_mode": "REGIONAL", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_router", "name": "router", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"bgp": [], "creation_timestamp": "2025-08-21T11:19:49.188-07:00", "description": "", "encrypted_interconnect_router": false, "id": "projects/switcher-studio-233517/regions/us-central1/routers/webrtc-platform-dev-vpc-router", "name": "webrtc-platform-dev-vpc-router", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "project": "switcher-studio-233517", "region": "us-central1", "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/regions/us-central1/routers/webrtc-platform-dev-vpc-router", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_router_nat", "name": "nat", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"auto_network_tier": "PREMIUM", "drain_nat_ips": [], "enable_dynamic_port_allocation": false, "enable_endpoint_independent_mapping": false, "endpoint_types": ["ENDPOINT_TYPE_VM"], "icmp_idle_timeout_sec": 30, "id": "switcher-studio-233517/us-central1/webrtc-platform-dev-vpc-router/webrtc-platform-dev-vpc-nat", "log_config": [{"enable": true, "filter": "ERRORS_ONLY"}], "max_ports_per_vm": 0, "min_ports_per_vm": 0, "name": "webrtc-platform-dev-vpc-nat", "nat_ip_allocate_option": "AUTO_ONLY", "nat_ips": [], "project": "switcher-studio-233517", "region": "us-central1", "router": "webrtc-platform-dev-vpc-router", "rules": [], "source_subnetwork_ip_ranges_to_nat": "ALL_SUBNETWORKS_ALL_IP_RANGES", "subnetwork": [], "tcp_established_idle_timeout_sec": 1200, "tcp_time_wait_timeout_sec": 120, "tcp_transitory_idle_timeout_sec": 30, "timeouts": null, "udp_idle_timeout_sec": 30}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.networking", "mode": "managed", "type": "google_compute_subnetwork", "name": "subnet", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"creation_timestamp": "2025-08-21T11:19:49.125-07:00", "description": "", "external_ipv6_prefix": "", "fingerprint": null, "gateway_address": "********", "id": "projects/switcher-studio-233517/regions/us-central1/subnetworks/webrtc-platform-dev-subnet", "internal_ipv6_prefix": "", "ip_cidr_range": "10.0.0.0/24", "ipv6_access_type": "", "ipv6_cidr_range": "", "log_config": [], "name": "webrtc-platform-dev-subnet", "network": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/global/networks/webrtc-platform-dev-vpc", "private_ip_google_access": true, "private_ipv6_google_access": "DISABLE_GOOGLE_ACCESS", "project": "switcher-studio-233517", "purpose": "PRIVATE", "region": "us-central1", "role": "", "secondary_ip_range": [], "self_link": "https://www.googleapis.com/compute/v1/projects/switcher-studio-233517/regions/us-central1/subnetworks/webrtc-platform-dev-subnet", "send_secondary_ip_range_if_empty": null, "stack_type": "IPV4_ONLY", "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.networking.google_compute_network.vpc"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.registry", "mode": "data", "type": "google_project", "name": "project", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"auto_create_network": null, "billing_account": "016427-C76683-997324", "deletion_policy": "DELETE", "effective_labels": {}, "folder_id": "", "id": "projects/switcher-studio-233517", "labels": {}, "name": "Switcher Studio", "number": "************", "org_id": "************", "project_id": "switcher-studio-233517", "skip_delete": null, "terraform_labels": {}}, "sensitive_attributes": [], "identity_schema_version": 0}]}, {"module": "module.webrtc_platform.module.registry", "mode": "managed", "type": "google_artifact_registry_repository", "name": "main", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"cleanup_policies": [], "cleanup_policy_dry_run": false, "create_time": "2025-08-21T18:19:18.057654Z", "description": "Docker repository for WebRTC platform containers", "docker_config": [], "effective_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "format": "DOCKER", "id": "projects/switcher-studio-233517/locations/us-central1/repositories/webrtc-platform-dev-repo", "kms_key_name": "", "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "location": "us-central1", "maven_config": [], "mode": "STANDARD_REPOSITORY", "name": "webrtc-platform-dev-repo", "project": "switcher-studio-233517", "remote_repository_config": [], "repository_id": "webrtc-platform-dev-repo", "terraform_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "timeouts": null, "update_time": "2025-08-21T19:05:59.612606Z", "virtual_repository_config": []}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"]}]}, {"module": "module.webrtc_platform.module.registry", "mode": "managed", "type": "google_artifact_registry_repository", "name": "npm", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"cleanup_policies": [], "cleanup_policy_dry_run": false, "create_time": "2025-08-21T20:20:51.652741Z", "description": "npm repository for WebRTC platform packages", "docker_config": [], "effective_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "format": "NPM", "id": "projects/switcher-studio-233517/locations/us-central1/repositories/webrtc-platform-dev-repo-npm", "kms_key_name": "", "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "location": "us-central1", "maven_config": [], "mode": "STANDARD_REPOSITORY", "name": "webrtc-platform-dev-repo-npm", "project": "switcher-studio-233517", "remote_repository_config": [], "repository_id": "webrtc-platform-dev-repo-npm", "terraform_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "timeouts": null, "update_time": "2025-08-21T20:20:51.652741Z", "virtual_repository_config": []}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"]}]}, {"module": "module.webrtc_platform.module.registry", "mode": "managed", "type": "google_artifact_registry_repository_iam_member", "name": "cloudbuild_writer", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85CDU7bM=", "id": "projects/switcher-studio-233517/locations/us-central1/repositories/webrtc-platform-dev-repo/roles/artifactregistry.writer/serviceAccount:<EMAIL>", "location": "us-central1", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "repository": "projects/switcher-studio-233517/locations/us-central1/repositories/webrtc-platform-dev-repo", "role": "roles/artifactregistry.writer"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.registry.data.google_project.project", "module.webrtc_platform.module.registry.google_artifact_registry_repository.main"]}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "media_artifact_registry_reader", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85dOQEIM=", "id": "switcher-studio-233517/roles/artifactregistry.reader/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/artifactregistry.reader"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.media_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "media_logging", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85dOQEIM=", "id": "switcher-studio-233517/roles/logging.logWriter/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/logging.logWriter"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.media_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "media_monitoring", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85dOQEIM=", "id": "switcher-studio-233517/roles/monitoring.metricWriter/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/monitoring.metricWriter"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.media_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "media_secretmanager", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85dOQEIM=", "id": "switcher-studio-233517/roles/secretmanager.secretAccessor/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/secretmanager.secretAccessor"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.media_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "media_storage", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85dOQEIM=", "id": "switcher-studio-233517/roles/storage.objectAdmin/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/storage.objectAdmin"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.media_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "signaling_logging", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85dOQEIM=", "id": "switcher-studio-233517/roles/logging.logWriter/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/logging.logWriter"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.signaling_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "signaling_monitoring", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85dOQEIM=", "id": "switcher-studio-233517/roles/monitoring.metricWriter/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/monitoring.metricWriter"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.signaling_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "signaling_secretmanager", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85dOQEIM=", "id": "switcher-studio-233517/roles/secretmanager.secretAccessor/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/secretmanager.secretAccessor"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.signaling_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "turn_logging", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85dOQEIM=", "id": "switcher-studio-233517/roles/logging.logWriter/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/logging.logWriter"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.turn_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "turn_monitoring", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85dOQEIM=", "id": "switcher-studio-233517/roles/monitoring.metricWriter/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/monitoring.metricWriter"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.turn_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_project_iam_member", "name": "turn_secretmanager", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85dOQEIM=", "id": "switcher-studio-233517/roles/secretmanager.secretAccessor/serviceAccount:<EMAIL>", "member": "serviceAccount:<EMAIL>", "project": "switcher-studio-233517", "role": "roles/secretmanager.secretAccessor"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_service_account.turn_server"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_secret_manager_secret", "name": "turn_password", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": {}, "create_time": "2025-08-21T18:19:16.464515Z", "effective_annotations": {}, "effective_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "expire_time": "", "id": "projects/switcher-studio-233517/secrets/webrtc-platform-dev-turn-password", "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "name": "projects/************/secrets/webrtc-platform-dev-turn-password", "project": "switcher-studio-233517", "replication": [{"auto": [{"customer_managed_encryption": []}], "user_managed": []}], "rotation": [], "secret_id": "webrtc-platform-dev-turn-password", "terraform_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "timeouts": null, "topics": [], "ttl": null, "version_aliases": {}, "version_destroy_ttl": ""}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_secret_manager_secret", "name": "turn_username", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"annotations": {}, "create_time": "2025-08-21T18:19:16.458762Z", "effective_annotations": {}, "effective_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "expire_time": "", "id": "projects/switcher-studio-233517/secrets/webrtc-platform-dev-turn-username", "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "name": "projects/************/secrets/webrtc-platform-dev-turn-username", "project": "switcher-studio-233517", "replication": [{"auto": [{"customer_managed_encryption": []}], "user_managed": []}], "rotation": [], "secret_id": "webrtc-platform-dev-turn-username", "terraform_labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1"}, "timeouts": null, "topics": [], "ttl": null, "version_aliases": {}, "version_destroy_ttl": ""}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_secret_manager_secret_version", "name": "turn_password", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"create_time": "2025-08-21T18:19:20.090514Z", "deletion_policy": "DELETE", "destroy_time": "", "enabled": true, "id": "projects/************/secrets/webrtc-platform-dev-turn-password/versions/1", "is_secret_data_base64": false, "name": "projects/************/secrets/webrtc-platform-dev-turn-password/versions/1", "secret": "projects/switcher-studio-233517/secrets/webrtc-platform-dev-turn-password", "secret_data": "webrtc123", "timeouts": null, "version": "1"}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_data"}]], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_secret_manager_secret_version", "name": "turn_username", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"create_time": "2025-08-21T18:19:21.040347Z", "deletion_policy": "DELETE", "destroy_time": "", "enabled": true, "id": "projects/************/secrets/webrtc-platform-dev-turn-username/versions/1", "is_secret_data_base64": false, "name": "projects/************/secrets/webrtc-platform-dev-turn-username/versions/1", "secret": "projects/switcher-studio-233517/secrets/webrtc-platform-dev-turn-username", "secret_data": "webrtc", "timeouts": null, "version": "1"}, "sensitive_attributes": [[{"type": "get_attr", "value": "secret_data"}]], "identity_schema_version": 0, "private": "****************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_service_account", "name": "media_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "webrtc-platform-dev-media", "create_ignore_already_exists": null, "description": "Service account for the WebRTC media server", "disabled": false, "display_name": "WebRTC Media Server Service Account", "email": "<EMAIL>", "id": "projects/switcher-studio-233517/serviceAccounts/<EMAIL>", "member": "serviceAccount:<EMAIL>", "name": "projects/switcher-studio-233517/serviceAccounts/<EMAIL>", "project": "switcher-studio-233517", "timeouts": null, "unique_id": "107029475094469952231"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_service_account", "name": "signaling_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "webrtc-platform-dev-signaling", "create_ignore_already_exists": null, "description": "Service account for the WebRTC signaling server", "disabled": false, "display_name": "WebRTC Signaling Server Service Account", "email": "<EMAIL>", "id": "projects/switcher-studio-233517/serviceAccounts/<EMAIL>", "member": "serviceAccount:<EMAIL>", "name": "projects/switcher-studio-233517/serviceAccounts/<EMAIL>", "project": "switcher-studio-233517", "timeouts": null, "unique_id": "103460412981079589180"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.security", "mode": "managed", "type": "google_service_account", "name": "turn_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"account_id": "webrtc-platform-dev-turn", "create_ignore_already_exists": null, "description": "Service account for the TURN server", "disabled": false, "display_name": "TURN Server Service Account", "email": "<EMAIL>", "id": "projects/switcher-studio-233517/serviceAccounts/<EMAIL>", "member": "serviceAccount:<EMAIL>", "name": "projects/switcher-studio-233517/serviceAccounts/<EMAIL>", "project": "switcher-studio-233517", "timeouts": null, "unique_id": "109752402079903820303"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "****************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis"], "create_before_destroy": true}]}, {"module": "module.webrtc_platform.module.signaling_server", "mode": "managed", "type": "google_cloud_run_service", "name": "signaling_server", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 2, "attributes": {"autogenerate_revision_name": false, "id": "locations/us-central1/namespaces/switcher-studio-233517/services/webrtc-platform-dev-signaling", "location": "us-central1", "metadata": [{"annotations": {}, "effective_annotations": {"run.googleapis.com/ingress": "all", "run.googleapis.com/ingress-status": "all", "run.googleapis.com/operation-id": "06377857-db47-4ca0-a193-ec797f3087ef", "run.googleapis.com/urls": "[\"https://webrtc-platform-dev-signaling-************.us-central1.run.app\",\"https://webrtc-platform-dev-signaling-7egrqrsfva-uc.a.run.app\"]", "serving.knative.dev/creator": "<EMAIL>", "serving.knative.dev/lastModifier": "<EMAIL>"}, "effective_labels": {"cloud.googleapis.com/location": "us-central1"}, "generation": 1, "labels": {}, "namespace": "switcher-studio-233517", "resource_version": "AAY85fvpXWw", "self_link": "/apis/serving.knative.dev/v1/namespaces/************/services/webrtc-platform-dev-signaling", "terraform_labels": {}, "uid": "71099fd1-86da-4f3d-96d7-86b28847442f"}], "name": "webrtc-platform-dev-signaling", "project": "switcher-studio-233517", "status": [{"conditions": [{"message": "", "reason": "", "status": "True", "type": "Ready"}, {"message": "", "reason": "", "status": "True", "type": "ConfigurationsReady"}, {"message": "", "reason": "", "status": "True", "type": "RoutesReady"}], "latest_created_revision_name": "webrtc-platform-dev-signaling-00001-cmh", "latest_ready_revision_name": "webrtc-platform-dev-signaling-00001-cmh", "observed_generation": 1, "traffic": [{"latest_revision": true, "percent": 100, "revision_name": "webrtc-platform-dev-signaling-00001-cmh", "tag": "", "url": ""}], "url": "https://webrtc-platform-dev-signaling-7egrqrsfva-uc.a.run.app"}], "template": [{"metadata": [{"annotations": {"autoscaling.knative.dev/maxScale": "3", "run.googleapis.com/execution-environment": "gen2", "run.googleapis.com/vpc-access-connector": "webrtc-dev-signaling-conn", "run.googleapis.com/vpc-access-egress": "private-ranges-only"}, "generation": 0, "labels": {"environment": "dev", "managed-by": "terraform", "project": "webrtc-platform", "region": "us-central1", "run.googleapis.com/startupProbeType": "<PERSON><PERSON><PERSON>"}, "name": "", "namespace": "", "resource_version": "", "self_link": "", "uid": ""}], "spec": [{"container_concurrency": 80, "containers": [{"args": [], "command": [], "env": [{"name": "MEDIA_SERVER_URL", "value": "https://*************:8080", "value_from": []}, {"name": "NODE_ENV", "value": "production", "value_from": []}, {"name": "TURN_PASSWORD", "value": "", "value_from": [{"secret_key_ref": [{"key": "latest", "name": "webrtc-platform-dev-turn-password"}]}]}, {"name": "TURN_SERVER_IP", "value": "************", "value_from": []}, {"name": "TURN_USERNAME", "value": "webrtc", "value_from": []}], "env_from": [], "image": "us-central1-docker.pkg.dev/switcher-studio-233517/webrtc-platform-dev-repo/webrtc-signaling:latest", "liveness_probe": [], "name": "", "ports": [{"container_port": 3001, "name": "http1", "protocol": ""}], "resources": [{"limits": {"cpu": "1", "memory": "1Gi"}, "requests": {}}], "startup_probe": [{"failure_threshold": 1, "grpc": [], "http_get": [], "initial_delay_seconds": 0, "period_seconds": 240, "tcp_socket": [{"port": 3001}], "timeout_seconds": 240}], "volume_mounts": [], "working_dir": ""}], "service_account_name": "<EMAIL>", "serving_state": "", "timeout_seconds": 300, "volumes": []}]}], "timeouts": null, "traffic": [{"latest_revision": true, "percent": 100, "revision_name": "", "tag": "", "url": ""}]}, "sensitive_attributes": [[{"type": "get_attr", "value": "template"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "spec"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "containers"}, {"type": "index", "value": {"value": 0, "type": "number"}}, {"type": "get_attr", "value": "env"}]], "identity_schema_version": 0, "private": "********************************************************************************************************************************************************************************", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.compute.google_compute_address.turn_server_ip", "module.webrtc_platform.module.compute.google_compute_health_check.turn_server", "module.webrtc_platform.module.compute.google_compute_instance.turn_server", "module.webrtc_platform.module.media_server.google_compute_address.media_server_ip", "module.webrtc_platform.module.media_server.google_compute_firewall.allow_media_server_https", "module.webrtc_platform.module.media_server.google_compute_firewall.allow_webrtc_media", "module.webrtc_platform.module.media_server.google_compute_instance.media_server", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server", "module.webrtc_platform.module.signaling_server.google_vpc_access_connector.connector"]}]}, {"module": "module.webrtc_platform.module.signaling_server", "mode": "managed", "type": "google_cloud_run_service_iam_member", "name": "signaling_public", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"condition": [], "etag": "BwY85fxlx5U=", "id": "v1/projects/switcher-studio-233517/locations/us-central1/services/webrtc-platform-dev-signaling/roles/run.invoker/allUsers", "location": "us-central1", "member": "allUsers", "project": "switcher-studio-233517", "role": "roles/run.invoker", "service": "v1/projects/switcher-studio-233517/locations/us-central1/services/webrtc-platform-dev-signaling"}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "bnVsbA==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.compute.google_compute_address.turn_server_ip", "module.webrtc_platform.module.compute.google_compute_health_check.turn_server", "module.webrtc_platform.module.compute.google_compute_instance.turn_server", "module.webrtc_platform.module.media_server.google_compute_address.media_server_ip", "module.webrtc_platform.module.media_server.google_compute_firewall.allow_media_server_https", "module.webrtc_platform.module.media_server.google_compute_firewall.allow_webrtc_media", "module.webrtc_platform.module.media_server.google_compute_instance.media_server", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server", "module.webrtc_platform.module.signaling_server.google_cloud_run_service.signaling_server", "module.webrtc_platform.module.signaling_server.google_vpc_access_connector.connector"]}]}, {"module": "module.webrtc_platform.module.signaling_server", "mode": "managed", "type": "google_vpc_access_connector", "name": "connector", "provider": "module.webrtc_platform.provider[\"registry.terraform.io/hashicorp/google\"]", "instances": [{"schema_version": 0, "attributes": {"connected_projects": ["switcher-studio-233517"], "id": "projects/switcher-studio-233517/locations/us-central1/connectors/webrtc-dev-signaling-conn", "ip_cidr_range": "********/28", "machine_type": "e2-micro", "max_instances": 3, "max_throughput": 300, "min_instances": 2, "min_throughput": 200, "name": "webrtc-dev-signaling-conn", "network": "webrtc-platform-dev-vpc", "project": "switcher-studio-233517", "region": "us-central1", "self_link": "projects/switcher-studio-233517/locations/us-central1/connectors/webrtc-dev-signaling-conn", "state": "READY", "subnet": [], "timeouts": null}, "sensitive_attributes": [], "identity_schema_version": 0, "private": "eyJlMmJmYjczMC1lY2FhLTExZTYtOGY4OC0zNDM2M2JjN2M0YzAiOnsiY3JlYXRlIjoxMjAwMDAwMDAwMDAwLCJkZWxldGUiOjEyMDAwMDAwMDAwMDB9fQ==", "dependencies": ["module.webrtc_platform.google_project_service.required_apis", "module.webrtc_platform.module.compute.google_compute_address.turn_server_ip", "module.webrtc_platform.module.compute.google_compute_health_check.turn_server", "module.webrtc_platform.module.compute.google_compute_instance.turn_server", "module.webrtc_platform.module.media_server.google_compute_address.media_server_ip", "module.webrtc_platform.module.media_server.google_compute_firewall.allow_media_server_https", "module.webrtc_platform.module.media_server.google_compute_firewall.allow_webrtc_media", "module.webrtc_platform.module.media_server.google_compute_instance.media_server", "module.webrtc_platform.module.networking.google_compute_firewall.allow_health_check", "module.webrtc_platform.module.networking.google_compute_firewall.allow_http_https", "module.webrtc_platform.module.networking.google_compute_firewall.allow_internal", "module.webrtc_platform.module.networking.google_compute_firewall.allow_ssh", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_relay", "module.webrtc_platform.module.networking.google_compute_firewall.allow_turn_stun", "module.webrtc_platform.module.networking.google_compute_network.vpc", "module.webrtc_platform.module.networking.google_compute_router.router", "module.webrtc_platform.module.networking.google_compute_router_nat.nat", "module.webrtc_platform.module.networking.google_compute_subnetwork.subnet", "module.webrtc_platform.module.security.google_project_iam_member.media_artifact_registry_reader", "module.webrtc_platform.module.security.google_project_iam_member.media_logging", "module.webrtc_platform.module.security.google_project_iam_member.media_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.media_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.media_storage", "module.webrtc_platform.module.security.google_project_iam_member.signaling_logging", "module.webrtc_platform.module.security.google_project_iam_member.signaling_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.signaling_secretmanager", "module.webrtc_platform.module.security.google_project_iam_member.turn_logging", "module.webrtc_platform.module.security.google_project_iam_member.turn_monitoring", "module.webrtc_platform.module.security.google_project_iam_member.turn_secretmanager", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret.turn_username", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_password", "module.webrtc_platform.module.security.google_secret_manager_secret_version.turn_username", "module.webrtc_platform.module.security.google_service_account.media_server", "module.webrtc_platform.module.security.google_service_account.signaling_server", "module.webrtc_platform.module.security.google_service_account.turn_server"]}]}], "check_results": null}